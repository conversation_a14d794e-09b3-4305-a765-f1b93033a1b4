<?php

namespace App\Models;

use App\Api\Manage\ManageService;
use App\Models\Enums\MatchCenterEnrollType;
use App\Models\Enums\MatchCenterStatus;
use App\Services\MatchService;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Carbon;


/**
 * @property  int $id
 * @property  array $other_certificates_urls
 * @property  array $sites
 * @property  Carbon $enroll_start_date
 * @property  Carbon $enroll_end_date
 * @property  Carbon $compete_start_date
 * @property  Carbon $compete_end_date
 */
class MatchCenter extends Model
{
    //
    public $guarded = [];

    //
    public $casts = [
        'other_certificates_urls' => 'json',
        'sites' => 'json',
        'enroll_start_date' => 'date',
        'enroll_end_date' => 'date',
        'compete_start_date' => 'date',
        'compete_end_date' => 'date',
        'status' => MatchCenterStatus::class,
        'enroll_type' => MatchCenterEnrollType::class
    ];
    public $needUpdate = true;

    protected static function booted()
    {

        static::updated(function (self $matchCenter) {
            if (!$matchCenter->isDirty('remote_id') && $matchCenter->enroll_type == MatchCenterEnrollType::MiniProgram && $matchCenter->needUpdate) {
                // 更新基础信息
                ManageService::sync2MiniProgram($matchCenter);
                // 更新用户信息
                ManageService::sync2MiniProgramUsers($matchCenter);
                // 同步项目-组织-级别
                ManageService::sync2MiniProgramProjects($matchCenter);
            }
        });
    }

    public function createUser(): BelongsTo
    {
        return $this->belongsTo(AdminUser::class, 'create_user_id');
    }

    public function projects(): HasMany
    {
        return $this->hasmany(MatchCenterProject::class);
    }

    public function referees(): HasMany
    {
        return $this->hasmany(Referee::class);
    }

    public function scopeCreatedAtBetween(Builder $builder, $start = null, $end = null): void
    {
        try {
            if ($start) {
                $builder->where('created_at', '>=', Carbon::parse($start));
            }
            if ($end) {
                $builder->where('created_at', '<=', Carbon::parse($end));
            }
        } catch (\Throwable $exception) {
        }
    }

    public function users(): HasMany
    {
        return $this->hasMany(User::class,'match_center_id');
    }
}
