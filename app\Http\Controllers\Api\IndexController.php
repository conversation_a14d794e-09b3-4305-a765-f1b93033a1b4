<?php

namespace App\Http\Controllers\Api;

use App\Model\DeviceModels;
use App\Model\UserModels;
use App\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use Illuminate\Support\Carbon;

class IndexController extends Controller
{
    public function index(Request $request)
    {
//        $value = session('admin');
//        return $value;
//        $user = DeviceModels::pluck('address');
//        $role = $request->input('role');
        $info = $request->all();
        // dd($info);
        $address = $info['address'];
        
        $key = 'a7b52be6ae5b405e639554ddb88bf6e9';
       $regeo_url="https://restapi.amap.com/v3/geocode/geo";
	$address_location=$regeo_url."?output=JSON&address=$address&key=$key";
// 	dd($address_location);
	$data_location=file_get_contents($address_location);
    // dd($data_location);
	$result_local=json_decode($data_location,true);
    // dd($result_local);
    //返回数据状态1 为成功 0 为失败
    $local_status=$result_local['status'];
    //返回状态码 10000 为正确 其他为错误
    $local_infocode=$result_local['infocode'];
     $address=array();
	if($local_status==1 && $local_infocode== 10000 ){
        //地址信息的数组
        $local_geocode=$result_local['geocodes'];


        $location=$local_geocode[0]['location'];
        echo "<pre>";
        print_r($location);

        $location_ay=explode(",",$location);
        echo "<pre>";
        print_r($location_ay);


    }else{
	    echo "不能查询出数据";
    }
        // $time=Carbon::now()->toDateTimeString();
        // $time = preg_replace('/[^\.0123456789]/s', '', $time);
        // dd($time);
        // $suiji = 'U'.time();
        // dd($suiji);
//        return view('admin.index');
    }
    
    //定时清理日志数据
    public function clear()
    {
        $endtime = time() - 7*24*60*60;
        // dd($time);
        $arr = [['created_at','<=',$endtime]];
        DB::table('plc_request_log')->where($arr)->delete();
    }
    
    //判断设备是否连接超时
    public function overtime()
    {
        $endtime = time() - 120;

        DB::table('plc_device_user')->where('time','<',$endtime)->update(['status'=>1]);
    }
}
