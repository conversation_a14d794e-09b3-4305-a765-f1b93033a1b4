<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('venue_machines', function (Blueprint $table) {
            $table->id();
            $table->string('venue_number', 50)->comment('场地号');
            $table->string('machine_number', 100)->comment('场地机编号');
            $table->text('remarks')->nullable()->comment('备注');
            $table->timestamp('last_report_time')->nullable()->comment('最后上报时间');
            $table->timestamps();
            
            // 添加索引
            $table->index('venue_number');
            $table->index('machine_number');
            $table->index('last_report_time');
            
            // 添加唯一约束，确保同一场地的机器编号不重复
            $table->unique(['venue_number', 'machine_number'], 'unique_venue_machine');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('venue_machines');
    }
};
