<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('users', function (Blueprint $table) {
            $table->id();
            $table->string('name')->comment('姓名');
            $table->string('team_name')->comment('队伍名称');
            $table->tinyInteger('card_type')->default(1)->comment('证件类别 :1身份证');
            $table->string('id_card')->comment('证件/护照');
            $table->tinyInteger('sex')->nullable()->comment('1:男2女');
            $table->string('image_url')->default("")->comment('照片');
            $table->bigInteger('match_center_id')->comment('所属赛事中心');
            $table->tinyInteger('type')->default(1)->comment('人员信息 1:运动员 2:队医,3:领队,4:教练');
            $table->tinyInteger('status')->default(1)->comment('状态 1:未发布 2:已参赛');

            $table->bigInteger('match_center_project_id')->nullable()->comment('参赛项目');
            $table->bigInteger('match_center_group_id')->nullable()->comment('组别名称');
            $table->bigInteger('match_center_level_id')->nullable()->comment('报名级别');
            $table->decimal('registration_fee')->nullable()->comment('费用');
            $table->datetime('payment_at')->nullable()->comment('缴费时间');
            $table->boolean('is_seeded_player')->comment('种子选手')->default(false);
            $table->softDeletes();
            $table->timestamps();
        });

        Schema::create('password_reset_tokens', function (Blueprint $table) {
            $table->string('email')->primary();
            $table->string('token');
            $table->timestamp('created_at')->nullable();
        });

        Schema::create('sessions', function (Blueprint $table) {
            $table->string('id')->primary();
            $table->foreignId('user_id')->nullable()->index();
            $table->string('ip_address', 45)->nullable();
            $table->text('user_agent')->nullable();
            $table->longText('payload');
            $table->integer('last_activity')->index();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('users');
        Schema::dropIfExists('password_reset_tokens');
        Schema::dropIfExists('sessions');
    }
};
