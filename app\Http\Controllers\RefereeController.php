<?php

namespace App\Http\Controllers;

use App\Models\AdminUser;
use App\Models\Enums\UserSex;
use App\Models\MatchCenter;
use App\Models\Referee;
use App\Utils\CommonUtil;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rules\Enum;
use Illuminate\Validation\ValidationException;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\QueryBuilder;

class RefereeController extends Controller
{
    //

    /**
     * @param Request $request
     * @return AnonymousResourceCollection
     */
    public function index(Request $request, MatchCenter $matchCenter): AnonymousResourceCollection
    {
        $builder = QueryBuilder::for(Referee::class)
            ->where('match_center_id', $matchCenter->id)
            ->allowedFilters([
                AllowedFilter::partial('name'),
                AllowedFilter::exact('sex'),
                AllowedFilter::partial('id_card'),
            ])
            ->defaultSort('-id');

        return JsonResource::collection($builder->paginate($this->getPerPage()));
    }

    /**
     * 详情
     * @param Request $request
     * @param Referee $Referee
     * @return JsonResource
     */
    public function show(Request $request, Referee $Referee): JsonResource
    {
        return JsonResource::make($Referee);
    }

    /**
     * 创建裁判
     * @param Request $request
     * @param Referee $referee
     * @return JsonResource
     */
    public function store(Request $request, Referee $referee, MatchCenter $matchCenter): JsonResource
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'company' => 'required|string|max:255',
            'id_card' => 'required|string|max:255',
            'sex' => ['required', new Enum(UserSex::class)],
            'level' => 'required|string|max:255',
            'level_weight' => 'int',
            'image_url' => 'string|max:255',
        ]);
        if (!CommonUtil::validateIDCard($validated['id_card'])) {
            throw ValidationException::withMessages([
                'id_card' => ['身份证格式错误!'],
            ]);
        }
        $validated['match_center_id'] = $matchCenter->id;
        $referee->fill($validated)->save();
        return JsonResource::make($referee);
    }

    /**
     * 编辑
     * @param Request $request
     * @param Referee $referee
     * @return JsonResource
     */
    public function update(Request $request, Referee $referee): JsonResource
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'company' => 'required|string|max:255',
            'id_card' => 'required|string|max:255',
            'sex' => ['required', new Enum(UserSex::class)],
            'level' => 'required|string|max:255',
            'level_weight' => 'int',
            'image_url' => 'string|max:255',
        ]);
        if (!CommonUtil::validateIDCard($validated['id_card'])) {
            throw ValidationException::withMessages([
                'id_card' => ['身份证格式错误!'],
            ]);
        }
        $referee->update($validated);
        return JsonResource::make($referee);
    }

    /**
     * 删除
     * @param Request $request
     * @param Referee $referee
     * @return JsonResource
     */
    public function destroy(Request $request, Referee $referee): JsonResource
    {
        $referee->delete();
        return JsonResource::make([]);
    }
}
