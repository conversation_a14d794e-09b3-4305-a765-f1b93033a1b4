<?php

namespace App\Http\Controllers;

use App\Jobs\OperationLog;
use App\Models\AdminUser;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\ValidationException;

class AuthController extends Controller
{
    //
    public function login(Request $request): JsonResource
    {
        $validated = $request->validate([
            'account' => 'required|string',
            'password' => 'required|string'
        ]);
        $admin = AdminUser::query()->where('account', Arr::get($validated, 'account'))->first();
        if (!$admin){
            throw ValidationException::withMessages([
                'account' => ['账号不存在.'],
            ]);
        }

        if (!Hash::check(Arr::get($validated, 'password'), $admin->password)) {
            throw ValidationException::withMessages([
                'password' => ['输入的密码错误.'],
            ]);
        }
        // token
        $expireAt = now()->addDays(30);
        $token = $admin->createToken('api', ['*'], $expireAt);

        // 日志随地大小插
        OperationLog::dispatch($admin, $request->ip(), "登录成功", $request->except(['password']));
        return JsonResource::make([
            'token' => $token->plainTextToken,
            'expire_at' => $expireAt
        ]);
    }
}
