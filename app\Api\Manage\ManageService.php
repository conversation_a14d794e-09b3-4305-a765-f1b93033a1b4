<?php

namespace App\Api\Manage;

use App\Models\Enums\UserStatus;
use App\Models\MatchCenter;
use App\Models\MatchCenterProject;
use App\Models\User;
use Illuminate\Support\Arr;

class ManageService
{

    public static function sync2MiniProgram(MatchCenter $matchCenter)
    {
        $client = new ManageClient();
        if (!$matchCenter->remote_id) {
            $res = $client->createMatchCenter([
                'title' => $matchCenter->title,
                'company_id' => 1
            ]);
            $matchCenter->remote_id = Arr::get($res, 'data.data.id');
            $matchCenter->needUpdate = false;
            $matchCenter->save();
        }

        $res = $client->updateMatchCenter($matchCenter->remote_id, [
            ...$matchCenter->only(['desc', 'logo_url', 'placard_url', 'status', 'title', 'level',
                'address', 'sponsor_company', 'sponsor_phone', 'guide_company', 'organizer_company', 'contest_regulations_url', 'participation_certificates_url',
                'other_certificates_urls', 'program_url', 'sites']),
            'enroll_start_date' => $matchCenter->enroll_start_date->toDateTimeString(),
            'enroll_end_date' => $matchCenter->enroll_end_date->toDateTimeString(),
            'compete_start_date' => $matchCenter->compete_start_date->toDateTimeString(),
            'compete_end_date' => $matchCenter->compete_end_date->toDateTimeString(),
            'company_id' => 1
        ]);
    }

    public static function sync2MiniProgramUsers(MatchCenter $matchCenter)
    {
        $client = new ManageClient();
        $data = $client->getMatchCenterRegistrations($matchCenter->remote_id);
        $users = Arr::get($data, 'data.data');
        $hasRemoteUser = $matchCenter->users()->get();
        foreach ($users as $user){
            $id = Arr::pluck($user, 'client_id');
            // 同步过的, 只需要更新
            if ($id && $cUser = $hasRemoteUser->where('id', $id)->first()) {
                // 更新
                $cUser->update([
                    'team_name' => Arr::get($user, 'team_name'),
                    'name' => Arr::get($user, 'name', "小程序用户" . random_int(0, 100)),
                    'card_type' => Arr::get($user, 'id_type'),
                    'id_card' => Arr::get($user, 'id_number'),
                    'sex' => Arr::get($user, 'sex'),
                    'image_url' => Arr::get($user, 'image_url'),
                    'type' => Arr::get($user, 'type'),
                    'match_center_id' => Arr::get($user, 'match_center_id'),
                    'match_center_group_id' => Arr::get($user, 'match_center_group_id'),
                    'match_center_project_id' => Arr::get($user, 'match_center_project_id'),
                    'match_center_level_id' => Arr::get($user, 'match_center_level_id'),
                ]);
            }else{
                // 创建新的
                User::query()->create( [
                    'remote_id' => Arr::get($user, 'id'),
                    'team_name' => Arr::get($user, 'team_name'),
                    'name' => Arr::get($user, 'name', "小程序用户" . random_int(0, 100)),
                    'card_type' => Arr::get($user, 'id_type'),
                    'id_card' => Arr::get($user, 'id_number'),
                    'sex' => Arr::get($user, 'sex'),
                    'image_url' => Arr::get($user, 'image_url'),
                    'match_center_id' => Arr::get($user, 'match_center_id'),
                    'type' => Arr::get($user, 'type'),
                    'match_center_group_id' => Arr::get($user, 'match_center_group_id'),
                    'match_center_project_id' => Arr::get($user, 'match_center_project_id'),
                    'match_center_level_id' => Arr::get($user, 'match_center_level_id'),
                    'status' => UserStatus::Unpublished,
                    'registration_fee' => 0,
                    'is_seeded_player' => 0,
                    'payment_at' => null,
                ]);
            }
        }

        $users = $matchCenter->users()->get();
        // 同步
        $client->syncMatchCenterRegistrations($matchCenter->remote_id,['items'=>$users->toArray()]);
    }

    public static function sync2MiniProgramProjects(MatchCenter $matchCenter)
    {
        $client = new ManageClient();
        $matchCenter->loadMissing([
            'projects:id,id as projectId,match_center_id,project,project as name',
            'projects.groups:id,id as groupId,name,match_center_project_id,max_birthday_date,min_birthday_date',
            'projects.groups.levels:id,id as levelId,level as name,match_center_group_id,level,sex',
            'projects.groups.levels.schedules:id,day,time,venue,match_center_level_id',
        ]);
        $projects = $matchCenter->projects->toArray();
        $client->updateMatchCenterProjectOptions($matchCenter->remote_id, ['project_structure' => $projects]);
    }
}
