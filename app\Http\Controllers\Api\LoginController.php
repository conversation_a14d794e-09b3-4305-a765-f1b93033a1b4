<?php

namespace App\Http\Controllers\Api;

//use App\Model\LoginModels;
//use App\Model\UserModels;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Http\Request;
use App\Http\Controllers\Api\Controller;
use Hash;
use App\Exports\TrafficExport;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Support\Facades\DB;
//use App\Http\Controllers\Controller;
//use Tymon\JWTAuth\Facades\JWTAuth;
//use Tymon\JWTAuth\Facades\JWTFactory;

class LoginController extends Controller
{
    public function login()
    {
//        return view('admin.login');
    }
    public function store(Request $request)
    {
//        dd($request->_token());
        $input = $request->all();
        // dd($input);
        $input = $request->except('_token');
        // dd($input);
        //验证规则
        $rule = [
            'username' => 'required|between:4,18',
            'password' => 'required|between:6,16'
        ];

        //返回错误
        $msg=[
            'username.required' =>'用户名必须输入',
            'username.between' =>'用户名长度必需在4-18位之间',
            'password.required' =>'密码必须输入',
            // 'password.between' =>'密码长度必需在6-16位之间',
            // 'password.alpha_dash' =>'密码必需是数字字母下划线',
        ];
        $validator = Validator::make($input,$rule,$msg);
        if ($validator->fails()) {
            return response()->json(['status'=>0,'msg'=>'登录失败！','data'=>$validator->errors()]);
        }

        $admin = DB::table('plc_admin')->where('account',$input['username'])->first();

        $admin = objToArr($admin);
        //判断用户名是否存在
        if(empty($admin)){

            //判断普通用户是否存在
            $admin = DB::table('plc_user')->where(['account'=>$input['username'],'del'=>null])->first();
            $admin = objToArr($admin);
//            dd($admin);
            if(!$admin){
                return response()->json(['status'=>0,'msg'=>'用户名错误！']);
            }
            //判断普通用户密码是否正确
            // dd(Crypt::decrypt($admin['password']));
            if($input['password'] != Crypt::decrypt($admin['password']))
            {
                return response()->json(['status'=>1,'msg'=>'密码错误！']);
            }
            // dd($admin);
            //判断账户是否启用
            if($admin['status'] == 1){
                return  response()->json(['status'=>1,'msg'=>'账户未启用，请联系管理员！']);
            }
            $token = encrypts(strval($admin['id']));
            $tokens = encrypts($admin['id'].time());
            Redis::set($token,$tokens);
            Redis::expire($token,3600*24*15);
            //4、保存用户信息到session中
//            session()->put('admin',$admin);
            //5、跳转到后台首页
//            return redirect('user/list');
//            $customClaims = ['sub' => ["account" =>$admin->account,'password'=>$admin->password]];
//            $payload = JWTFactory::customClaims($customClaims)->make();
//            $token = JWTAuth::encode($payload)->get();
//            $token = md5($admin['sort']);
//            dd($token);
            if (!$token) {
                return static::error("账号或密码错误", 4001);
            }
            $admin = DB::table('plc_user')->where(['account'=>$input['username']])->get();
            $admin = objToArr($admin);
            if($admin[0]['one'] == '0')
            {
                DB::table('plc_user')->where(['account'=>$input['username']])->update(['one'=>1]);
                return response()->json(['status'=>0,'msg'=>'登录成功,请修改密码！','token'=>$tokens,'role'=>$admin[0]['role']]);
            }
//            $admin['token'] = $token;
            return response()->json(['status'=>0,'msg'=>'登录成功！','token'=>$tokens,'role'=>$admin[0]['role']]);
//            return static::success($admin, "登录成功", 200, $token);
//            return response()->json(['status'=>1,'msg'=>'登录成功！','data'=>$admin]);
//            return redirect('device/index');
        }

        //判断密码是否正确
        if($input['password'] != Crypt::decrypt($admin['password']))
        {
            return response()->json(['status'=>1,'msg'=>'密码错误！']);
        }

        //4、保存用户信息到session中
//        session()->put('admin',$admin);

//        $customClaims = ['sub' => ["account" =>$admin->account,'password'=>$admin->password]];
//        $payload = JWTFactory::customClaims($customClaims)->make();
//        $token = JWTAuth::encode($payload)->get();
//        dd($token);

//        dd($token);
        $token = encrypts(strval($admin['sort']));
//        dd($token);
        $tokens = encrypts($admin['sort'].time());
//        dd($tokens);
        Redis::set($token,$tokens);
//        dd($tokens);
        Redis::expire($token,3600*24*15);
        if (!$token) {
            return static::error("账号或密码错误", 4001);
        }
//        $admin['token'] = $token;
        return response()->json(['status'=>0,'msg'=>'登录成功！','token'=>$tokens,'role'=>$admin['role']]);
//        return static::success($admin, "登录成功", 200, $token);
        //5、跳转到后台首页
//        return redirect('user/list');
//        $role = $admin['role'];
//        $role = $request->input('role');
//        dd($role);
//        return view('admin.index',compact('admin','request'));
//        return response()->json(['status'=>1,'msg'=>'登录成功！','data'=>$admin]);
    }

    //退出登录
    public function logout(Request $request)
    {
//        dd('123');
        //清空session中的用户信息
        // session()->flush();
        //跳转到登录页面
        // dd('123');
        $token = $request->header('token');
        // dd($token);
        $token = '';
        // dd($token);

        return response()->json(['status'=>0,'msg'=>'退出成功！','token'=>$token]);
    }
}
