<?php

namespace App\Models\Enums\MatchCenter;


enum ProjectSourceType: int
{
    case Personal = 1;
    case Double = 2;
    case Team = 3;
    case MixedTeam = 4;
    case MixedDouble = 5;

    public function desc(): string
    {
        return match ($this) {
            self::Personal => '个人',
            self::Double => '双人',
            self::Team => '团体',
            self::MixedTeam => '混团',
            self::MixedDouble => '混双',
        };
    }
}
