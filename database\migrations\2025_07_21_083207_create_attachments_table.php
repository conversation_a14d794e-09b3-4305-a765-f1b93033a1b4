<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('attachments', function (Blueprint $table) {
            $table->id();
            $table->string('sign')->index()->comment("文件md5值");
            $table->string('file_name')->default('')->comment("文件名");
            $table->string('file_type', 255)->default('')->comment("文件类型");
            $table->integer('file_size')->default(0)->comment("文件大小，单位字节");
            $table->text('path')->comment("文件存储路径");
            $table->string('disk')->default('public')->comment("文件存储驱动");
            $table->bigInteger('user_id')->nullable()->comment("上传用户");
            $table->timestamps();

            $table->comment('附件表');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('attachments');
    }
};
