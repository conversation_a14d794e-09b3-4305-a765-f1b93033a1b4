<?php

namespace App\Http\Controllers;

use App\Http\Resources\OperationLogResource;
use App\Models\OperationLog;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Resources\Json\JsonResource;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\QueryBuilder;

class OperationLogController extends Controller
{
    //

    public function index(Request $request): AnonymousResourceCollection
    {
        $builder = QueryBuilder::for(OperationLog::class)
            ->allowedFilters([
                AllowedFilter::partial('name', 'adminUser.name'),
                AllowedFilter::partial('account', 'adminUser.account'),
                AllowedFilter::partial('ip', 'ip'),
                AllowedFilter::scope('created_at_between'),
                AllowedFilter::callback('role_name', function (Builder $b, $roleName) {
                    $b->whereHas('adminUser', function (Builder $b2) use ($roleName) {
                        $b2->wherehas('roles', function (Builder $b3) use ($roleName) {
                            $b3->whereLike('name_zh', "%{$roleName}%");
                        });
                    });
                }),
            ])
            ->with([
                'adminUser',
                'adminUser.roles:id,name,name_zh'
            ])
            ->defaultSort('-id');

        return OperationLogResource::collection($builder->paginate($this->getPerPage()));
    }
}
