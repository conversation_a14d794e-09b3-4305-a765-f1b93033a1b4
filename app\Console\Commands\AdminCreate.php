<?php

namespace App\Console\Commands;

use App\Models\AdminUser;
use App\Models\Role;
use Illuminate\Console\Command;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\DB;

class AdminCreate extends Command
{

    protected $signature = 'admin:create';


    protected $description = '快速创建用户并生成token';


    public function __construct()
    {
        parent::__construct();
    }


    public function handle()
    {
        start:
        do {
            $account = $this->ask('请输入账号名');
            $exists = AdminUser::query()->where('account', $account)->exists();
            if ($exists) {
                $this->error("账号名重复,请重新输入");
            }
        } while ($exists);
        $password = $this->ask('请输入密码');
        $this->info("请选择角色:");
        $roles = Role::query()->pluck('name_zh', 'id');
        foreach ($roles as $id => $role) {
            $this->info(" ID:{$id} - {$role}");
        }

        $askRoles = explode(',', $this->ask('选择角色ID(多个用,分割)'));
        $askRoles = array_map(fn($id) => (int)$id, $askRoles);
        /**
         * @var AdminUser $user
         */
        DB::beginTransaction();
        try {
            $user = AdminUser::query()
                ->create([
                    'account' => $account,
                    'name' => $account,
                    'password' => $password,
                    'enabled' => true,
                ]);
            $askRoles && $user->syncRoles($askRoles);
            $expireAt = now()->addDays(30);
            $token = $user->createToken('sanctum', ['*'], $expireAt);
            $this->info('Bearer ' . $token->plainTextToken);

            DB::commit();
        } catch (\Throwable $throwable) {
            DB::rollBack();
            $this->error("参数异常,请重试" . $throwable->getMessage());
            goto start;
        }
        $this->info("添加成功");
    }
}
