<?php

namespace App\Jobs;

use App\Models\AdminUser;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class OperationLog implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct(public AdminUser $user, public string $ip, public string $content, public array $param)
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        // 创建日志
        \App\Models\OperationLog::query()->create([
            'ip' => $this->ip,
            'content' => $this->content,
            'param' => $this->param,
            'admin_user_id' => $this->user->id
        ]);
    }
}
