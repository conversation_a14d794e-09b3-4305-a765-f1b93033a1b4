<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('match_centers', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('remote_id')->nullable()->index();
            $table->string('title')->comment('赛事名称');
            $table->text('desc')->nullable()->comment('赛事简介');
            $table->unsignedBigInteger('create_user_id')->comment('赛事创建人信息');
            $table->string('logo_url')->nullable();
            $table->string('placard_url')->nullable()->comment('赛事海报');
            $table->integer('status')->comment('赛事状态');
            $table->integer('level')->nullable()->comment('赛事级别');
            $table->dateTime('enroll_start_date')->nullable()->comment('报名时间');
            $table->dateTime('enroll_end_date')->nullable();
            $table->dateTime('compete_start_date')->nullable()->comment('参赛时间');
            $table->dateTime('compete_end_date')->nullable();
            $table->string('address')->nullable()->comment('比赛地址');
            $table->string('sponsor_company')->nullable()->comment('主办方单位');
            $table->string('sponsor_phone')->nullable()->comment('主办方电话');
            $table->string('guide_company')->nullable()->comment('指导单位');
            $table->string('organizer_company')->nullable()->comment('承办方单位');
            $table->string('contest_regulations_url')->nullable()->comment('竞赛规程');
            $table->string('participation_certificates_url')->nullable()->comment('参赛证件');
            $table->json('other_certificates_urls')->nullable()->comment('其他证件');
            $table->string('program_url')->nullable()->comment('秩序册');
            $table->string('enroll_type')->nullable()->comment('参赛类别');
            $table->json('sites')->nullable()->comment('场地');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('match_centers');
    }
};
