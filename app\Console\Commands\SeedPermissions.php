<?php

namespace App\Console\Commands;

use App\Constants\Permissions;
use App\Models\Permission;
use Illuminate\Console\Command;
use Spatie\Permission\PermissionRegistrar;

class SeedPermissions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'permissions:seed';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '将 Permissions 枚举中定义的权限添加到数据库';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('开始添加权限到数据库...');

        // 清除权限缓存
        app(PermissionRegistrar::class)->forgetCachedPermissions();

        // 获取所有权限定义
        $permissions = Permissions::list();

        $this->info('找到 ' . count($permissions) . ' 个权限定义');

        // 创建权限
        foreach ($permissions as $permissionName => $description) {
            $permission = Permission::query()->updateOrCreate(
                [
                    'name' => $permissionName,
                    'guard_name' => 'admin'
                ],
                [
                    'desc' => $description,
                    'guard_name' => 'admin'
                ]
            );

            $this->line("✓ 权限 '{$permissionName}' - {$description}");
        }

        // 重新缓存权限
        app(PermissionRegistrar::class)->forgetCachedPermissions();

        $this->info('权限添加完成！');
        
        return Command::SUCCESS;
    }
}
