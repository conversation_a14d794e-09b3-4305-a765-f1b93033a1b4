<?php

namespace App\Models;

use App\Models\Enums\MatchCenterStatus;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;

class MatchDetail extends Model
{
    //
    //
    public $guarded = [];

    //
    public $casts = [
        'match_at' => 'date',
    ];

    /**
     * 参赛选手1
     * @return BelongsTo
     */
    public function user1(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user1_id');
    }

    /**
     * 参赛选手2
     * @return BelongsTo
     */
    public function user2(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user2_id');
    }

    /**
     * 上一轮场次
     * @return BelongsTo
     */
    public function parentMatch1(): BelongsTo
    {
        return $this->belongsTo(self::class, 'parent_match1_id');
    }

    /**
     * 上一轮场次
     * @return BelongsTo
     */
    public function parentMatch2(): BelongsTo
    {
        return $this->belongsTo(self::class, 'parent_match2_id');
    }

    /**
     * 本轮胜出用户
     * @return BelongsTo
     */
    public function winnerUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'winner_user_id');
    }

    /**
     * 裁判1
     * @return BelongsTo
     */
    public function referee1(): BelongsTo
    {
        return $this->belongsTo(Referee::class, 'referee1_id');
    }

    public function referee2(): BelongsTo
    {
        return $this->belongsTo(Referee::class, 'referee2_id');
    }

    public function referee3(): BelongsTo
    {
        return $this->belongsTo(Referee::class, 'referee3_id');
    }

    public function referee4(): BelongsTo
    {
        return $this->belongsTo(Referee::class, 'referee4_id');
    }

    /**
     * 坐台裁判
     * @return BelongsTo
     */
    public function refereeT(): BelongsTo
    {
        return $this->belongsTo(Referee::class, 'referee_t_id');
    }

    public function matchCenterGroupLevel(): BelongsTo
    {
        return $this->belongsTo(MatchCenterGroupLevel::class,'match_center_level_id');
    }

    public function matchCenterGroup(): BelongsTo
    {
        return $this->belongsTo(MatchCenterGroup::class);
    }

    public function matchCenterProject(): BelongsTo
    {
        return $this->belongsTo(MatchCenterProject::class);
    }

    public function matchCenter(): BelongsTo
    {
        return $this->belongsTo(MatchCenter::class);
    }

    public function schedule(): HasOne
    {
        return $this->hasOne(MatchCenterLevelSchedule::class, 'match_detail_id');
    }


}
