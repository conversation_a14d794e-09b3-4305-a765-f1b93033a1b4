<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class VenueMachine extends Model
{
    use HasFactory;

    /**
     * 表名
     */
    protected $table = 'venue_machines';

    /**
     * 可批量赋值的属性
     */
    protected $fillable = [
        'venue_number',
        'machine_number',
        'remarks',
        'last_report_time',
    ];

    /**
     * 属性类型转换
     */
    protected $casts = [
        'last_report_time' => 'datetime',
    ];

    /**
     * 日期序列化格式
     */
    protected $dateFormat = 'Y-m-d H:i:s';

    /**
     * 为JSON序列化时格式化日期
     */
    protected function serializeDate(\DateTimeInterface $date): string
    {
        return $date->format('Y-m-d H:i:s');
    }

    /**
     * 根据机器编号查找或创建记录
     *
     * @param string $venueNumber 场地号
     * @param string $machineNumber 场地机编号
     * @param string|null $remarks 备注
     * @return VenueMachine
     */
    public static function reportMachine(string $venueNumber, string $machineNumber, ?string $remarks = null): VenueMachine
    {
        return static::updateOrCreate(
            ['machine_number' => $machineNumber],
            [
                'venue_number' => $venueNumber,
                'remarks' => $remarks,
                'last_report_time' => Carbon::now(),
            ]
        );
    }

    /**
     * 获取格式化的最后上报时间
     */
    public function getFormattedLastReportTimeAttribute(): string
    {
        return $this->last_report_time ? $this->last_report_time->format('Y-m-d H:i:s') : '';
    }
}
