<?php

namespace App\Http\Middleware;

use App\Exceptions\BaseException;
use Closure;
use Illuminate\Http\Request;

class JsonResponseCode
{
    public function handle(Request $request, Closure $next)
    {
        /**
         * @var  \Illuminate\Http\JsonResponse $response
         */
        $response = $next($request);
        // API 增加code
        if ($response->headers->get('content-type') == 'application/json') {
            if (!$response->exception instanceof BaseException) {
                $rawData['data'] = $response->getData(true);
                $rawData['code'] = $response->getStatusCode();
                if ($response->exception instanceof \Throwable) {
                    $rawData['message'] = $response->exception->getMessage();
                }
                $response->setData($rawData);
            }

            $response->setStatusCode(200);
        }
        return $response;
    }
}
