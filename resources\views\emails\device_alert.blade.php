<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设备报警通知</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f4f4f4;
        }
        .container {
            background-color: #ffffff;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            border-bottom: 2px solid #e74c3c;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #e74c3c;
            margin: 0;
            font-size: 24px;
        }
        .alert-icon {
            font-size: 48px;
            color: #e74c3c;
            margin-bottom: 10px;
        }
        .content {
            margin-bottom: 30px;
        }
        .device-info {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            border-left: 4px solid #e74c3c;
            margin: 20px 0;
        }
        .device-info h3 {
            margin-top: 0;
            color: #2c3e50;
        }
        .alert-content {
            background-color: #fff5f5;
            padding: 15px;
            border-radius: 5px;
            border: 1px solid #fed7d7;
            margin: 15px 0;
        }
        .timestamp {
            color: #666;
            font-size: 14px;
            text-align: right;
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #eee;
        }
        .footer {
            text-align: center;
            color: #666;
            font-size: 12px;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
        }
        .company-name {
            color: #2c3e50;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="alert-icon">⚠️</div>
            <h1>设备报警通知</h1>
        </div>
        
        <div class="content">
            <p>您好，</p>
            <p>您的设备出现了需要关注的情况，请及时处理。</p>
            
            <div class="device-info">
                <h3>设备信息</h3>
                <p><strong>设备编号：</strong>{{ $deviceName }}</p>
            </div>
            
            <div class="alert-content">
                <h3 style="color: #e74c3c; margin-top: 0;">报警内容</h3>
                <p>{{ $alertContent }}</p>
            </div>
            
            <p>请您及时登录系统查看详细信息并处理相关问题。</p>
        </div>
        
        <div class="timestamp">
            报警时间：{{ $timestamp }}
        </div>
        
        <div class="footer">
            <p class="company-name">蹄伺令(青岛)创新科技有限公司</p>
            <p>此邮件为系统自动发送，请勿回复。</p>
        </div>
    </div>
</body>
</html>
