<?php

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::get('/', function () {
    return view('welcome');
});

Route::group(['middleware'=>'apiRoute'], function(){
//    Route::post('login', 'AuthController@login');
//    Route::post('login/store','Api\LoginController@store');
//    Route::get('device','Api\DeviceController@index');
//    Route::get('user/list','Api\UserController@list');

//登录页面路由
    Route::get('login','Api\LoginController@login');
//登录操作路由
    Route::post('login/store','Api\LoginController@store');


//设备信息路由
//Route::resource('device','Api\DeviceController');
    Route::get('device','Api\DeviceController@index');
    //设备添加页面路由
    Route::get('device/create','Api\DeviceController@create');
    //设备添加操作路由
    Route::post('device/store','Api\DeviceController@store');
//编辑页面路由
    Route::get('device/edit','Api\DeviceController@edit');
//    设备修改操作路由
    Route::post('device/update','Api\DeviceController@update');
//设备删除路由
    Route::post('device/del','Api\DeviceController@del');
//设备详情路由
     Route::post('device/details','Api\DeviceController@details');

//大屏展示路由
    Route::post('index','Api\IndexController@index');

//退出登录路由
    Route::get('logout','Api\LoginController@logout');




//企业信息路由
    Route::get('user/list','Api\UserController@list');
//新增页面路由
//    Route::get('add','UserController@add');
//新增操作路由
    Route::post('user/store','Api\UserController@store');
    //更改状态路由
    Route::post('user/state','Api\UserController@state');
//编辑页面路由
    Route::get('user/edit','Api\UserController@edit');
//编辑操作路由
    Route::post('user/update','Api\UserController@update');
//删除操作接口
    Route::post('user/del','Api\UserController@del');
//重置密码接口
    Route::post('user/chongzhi','Api\UserController@chongzhi');

//设备详情
    //设备详情首页
    Route::post('deatails/index','Api\DetailsController@index');
    //设备使用时间
    Route::post('deatails/setUP','Api\DetailsController@setUP');
    Route::post('deatails/setUPs','Api\DetailsController@setUPs');
    //报警设置
    Route::post('deatails/errorSET','Api\DetailsController@errorSET');
    //重启
    Route::post('restart','Api\MqttController@restart');
    //修改
    Route::post('deviceEdit', 'Api\DeviceController@deviceEdit');//泵余量修改
    Route::post('usagetime', 'Api\DeviceController@usagetime');//使用时间设置
    Route::post('dingshi', 'Api\DeviceController@dingshi');//定时启动设置
    Route::post('changyong', 'Api\DeviceController@changyong');//常用设置修改
    Route::post('setuperror', 'Api\DeviceController@setuperror');//报警设置修改
    Route::post('chushi', 'Api\DeviceController@chushi');//初始设置修改


//报错管理路由
    Route::get('error/list','Api\ErrorController@list');


//系统管理页面路由
    Route::get('admin/index','Api\AdminController@index');
//系统管理修改页面路由
//Route::get('admin/edit','Api\AdminController@edit');
//系统管理修改操作路由
    Route::post('admin/update','Api\AdminController@update');
    Route::post('admin/updates','Api\AdminController@updates');
});
Route::get('clear','Api\IndexController@clear');
Route::post('modbus','Api\DebugController@modbus');
Route::post('mqtt', 'Api\MqttController@mqtt');
Route::post('cesi', 'Api\MqttController@mqtt');
Route::post('fasong', 'Api\MqttController@fasong');
Route::post('message', 'Home\CodeController@message');
Route::get('dingshifs', 'Api\MqttController@dingshifs');
Route::get('overtime', 'Api\IndexController@overtime');
Route::get('deviceError', 'Api\ErrorController@deviceError');//监测设备问题并报警

Route::post('cesifasong', 'Api\DeviceController@cesifasong');//初始设置修改
Route::get('Set', 'Api\MqttController@Set');

Route::get('dingshiout', 'Api\DeviceController@dingshiout');//定时启动设置执行

// 邮件测试页面
Route::get('email-test', function () {
    return view('email_test');
});

// 邮件和智能通知测试路由 - 移到API路由避免CSRF问题
Route::group(['prefix' => 'api/test', 'namespace' => 'Api'], function() {
    Route::post('email', 'EmailTestController@testEmail'); // 测试邮件发送
    Route::post('device-alert', 'EmailTestController@testDeviceAlert'); // 测试设备报警邮件
    Route::get('ip-location', 'EmailTestController@testIpLocation'); // 测试IP地理位置检测
    Route::post('smart-notification', 'EmailTestController@testSmartNotification'); // 测试智能通知
    Route::get('mail-config', 'EmailTestController@getMailConfig'); // 获取邮件配置
});
