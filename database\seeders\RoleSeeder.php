<?php

namespace Database\Seeders;

use App\Models\Role;
use Illuminate\Database\Seeder;
use Spatie\Permission\PermissionRegistrar;

class RoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        foreach (Role::ROLE_LIST as $role => $roleZh) {
            Role::query()->updateOrCreate(['name' => $role], ['desc' => $roleZh,'guard_name' => 'admin']);
        }
        app(PermissionRegistrar::class)->forgetCachedPermissions();

    }
}
