<?php

namespace App\Http\Middleware;

use App\Enums\ErrorCode;
use App\Exceptions\DataException;
use App\Models\User;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class UserCheck
{

    public function handle(Request $request, Closure $next)
    {
        /**
         * @var  $user User
         */
        $user = Auth::user();
        if (!$user instanceof User) {
            throw new DataException("用户登录token无效或已过期", ErrorCode::Unauthenticated);
        }
//        if ($user->{'status'} == UserStatusEnum::StatusDisabled) {
//            throw new DataException("账号已被禁用,请联系管理员查看原因", ErrorCode::UserStatusDisabled);
//        }
//        if ($user->{'status'} == UserStatusEnum::StatusDestroyed) {
//            throw new DataException("账号注销", ErrorCode::UserStatusDestroyed);
//        }

        return $next($request);
    }
}
