<?php

namespace App\Http\Middleware;

use App\Enums\ErrorCode;
use App\Exceptions\DataException;
use App\Models\AdminUser;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class AdminCheck
{

    public function handle(Request $request, Closure $next)
    {
        /**
         * @var  $user AdminUser
         */
        $user = Auth::user();
        if (!$user instanceof AdminUser) {
            throw new DataException("用户登录token无效或已过期", ErrorCode::Unauthenticated);
        }
        if (!$user->enable) {
            throw new DataException("账号已被禁用,请联系管理员查看原因", ErrorCode::UserStatusDisabled);
        }

        return $next($request);
    }
}
