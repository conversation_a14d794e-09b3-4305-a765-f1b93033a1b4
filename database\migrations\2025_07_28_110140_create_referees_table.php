<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('referees', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('match_center_id')->comment('赛事中心id');
            $table->string('name')->comment('姓名');
            $table->string('company')->comment('单位');
            $table->tinyInteger('sex')->nullable()->comment('1:男2女');
            $table->string('level')->nullable()->comment('级别');
            $table->string('id_card')->nullable()->comment('身份证');
            $table->integer('level_weight')->default(50)->comment('级别权重');
            $table->string('image_url')->default("")->comment('照片');
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('referees');
    }
};
