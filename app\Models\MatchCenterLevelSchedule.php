<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class MatchCenterLevelSchedule extends Model
{
    //
    public $guarded = [];
    public $appends = [
        'time_string',
    ];

    public function timeString(): Attribute
    {
        return Attribute::get(function () {
            $hour = (int)Str::before($this->time, ':');
            if ($hour >= 12) {
                return "下午";
            }
            return "上午";
        });
    }

}
