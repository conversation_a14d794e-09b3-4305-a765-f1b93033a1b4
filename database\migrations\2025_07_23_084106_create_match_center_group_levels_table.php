<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('match_center_group_levels', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('match_center_group_id')->index()->comment('所属项目');
            $table->string('sex')->comment('性别,1男 2女');
            $table->string('level')->comment('界别');
            $table->integer('each_group_count')->default(2)->comment('每组人数');
            $table->tinyInteger('status')->default(0)->comment('0:为抽签, 1选手已抽签,2裁判已抽签');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('match_center_group_levels');
    }
};
