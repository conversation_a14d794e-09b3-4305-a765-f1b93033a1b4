<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('match_details', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('match_center_level_id')->index()->comment('所属赛事级别id');
            $table->bigInteger('match_center_group_id')->index()->comment('所属赛事组id');
            $table->bigInteger('match_center_project_id')->index()->comment('所属赛事项目id');
            $table->bigInteger('match_center_id')->index()->comment('所属赛事id');

            $table->string('round')->nullable()->comment('对阵信息');
            $table->integer('group')->default(1)->comment('组');
            $table->integer('total_step')->comment('总阶段');
            $table->integer('step')->comment('当前阶段');
            $table->bigInteger('user1_id')->nullable()->comment('选手1id');
            $table->bigInteger('user2_id')->nullable()->comment('选手2id,可能轮空');
            $table->bigInteger('parent_match1_id')->nullable()->comment('上一轮对战1的id');
            $table->bigInteger('parent_match2_id')->nullable()->comment('上一轮对战2的id');
            $table->dateTime('match_at')->nullable()->comment('比赛时间');
            $table->bigInteger('winner_user_id')->nullable()->comment('胜方用户id');

            $table->bigInteger('referee1_id')->nullable()->comment('裁判1');
            $table->bigInteger('referee2_id')->nullable()->comment('裁判2');
            $table->bigInteger('referee3_id')->nullable()->comment('裁判3');
            $table->bigInteger('referee4_id')->nullable()->comment('裁判4');
            $table->bigInteger('referee_t_id')->nullable()->comment('坐台裁判');

            $table->timestamps();
            $table->comment('对阵信息表');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('match_details');
    }
};
