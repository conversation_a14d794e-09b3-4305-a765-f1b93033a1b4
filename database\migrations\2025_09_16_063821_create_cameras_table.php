<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cameras', function (Blueprint $table) {
            $table->id();
            $table->string('venue_number', 50)->comment('关联场地号');
            $table->string('camera_mac', 100)->unique()->comment('摄像机MAC地址');
            $table->timestamp('last_report_time')->nullable()->comment('最后一次上报时间');
            $table->text('remarks')->nullable()->comment('备注');
            $table->timestamps();
            
            // 添加索引
            $table->index('venue_number');
            $table->index('camera_mac');
            $table->index('last_report_time');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cameras');
    }
};
