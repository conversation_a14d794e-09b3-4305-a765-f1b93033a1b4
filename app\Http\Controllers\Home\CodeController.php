<?php
namespace App\Http\Controllers\Home;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redis;
use Exception;
use App\Http\Controllers\CosPhpController;
class CodeController extends Controller
{
    protected $account = 'OT00724';
    protected $password = 'anwd87v4';

    public function sendCode($info)//发送验证码
    {

        $phone = $info['phone'];
        $device = $info['devicename'];
        $contents = $info['contents'];

        $content="【蹄伺令(青岛)创新科技有限公司】您的".$device."：".$contents.",请您及时处理";
        $body=[
                'action'=>'send',
                'account'=>$this->account,
                'password'=>$this->password,
                'mobile'=>$phone,
                'content'=>$content,                
        ];
        // dd($body);
        $url = 'https://dx.ipyy.net/smsJson.aspx';

        $res = $this->httpRequest($url,$body);
        return response()->json(['result'=>1,'message'=>'成功']);
    }

    function httpRequest($url, $postData=[])
    {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        if(!empty($postData)){
            // 设置POST请求的选项
            curl_setopt($ch, CURLOPT_POST, true);
            // 设置POST请求传递的数据
            curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
        }
        // 注意:如果发送https协议请求，禁用环境的的SSL证书认证。
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);
        // 设置请求头信息
        $header = ['Accept-Charset: utf-8'];
        curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
        $result = curl_exec($ch);
        curl_close($ch);
        return $result;
    }
    public function sendNotics($phone,$contents)
    {
        
        $content="【科贝美】补货通知:".$contents;
        $body=[
                'action'=>'send',
                'account'=>$this->account,
                'password'=>$this->password,
                'mobile'=>$phone,
                'content'=>$content,                
        ];
        $url = 'https://dx.ipyy.net/smsJson.aspx';

        $res = httpRequest($url,$body);
        $res = json_decode($res);
        return ['result'=>1,'message'=>'成功'];
    }


}