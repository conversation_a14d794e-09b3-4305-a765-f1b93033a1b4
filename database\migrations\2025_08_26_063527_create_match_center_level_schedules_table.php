<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('match_center_level_schedules', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('match_center_level_id')->comment('所属比赛层级id');
            $table->date('day')->comment('比赛日期');
            $table->time('time')->comment('比赛日期时间');
            $table->string('venue')->nullable()->comment('比赛场地');
            $table->bigInteger('match_detail_id')->nullable()->comment('分配比赛详情id');
            $table->timestamps();
            $table->comment('比赛日期分配表');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('match_center_level_schedules');
    }
};
