<?php

namespace App\Http\Controllers\MiniProgram;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\ValidationException;

class AuthController extends Controller
{
    //

    public function login(Request $request): JsonResource
    {
        $validated = $request->validate([
//            'account' => 'required|string',
//            'password' => 'required|string'
        ]);
        $user = User::query()->first();
//        if (!Hash::check(Arr::get($validated, 'password'), $user->password)) {
//            throw ValidationException::withMessages([
//                'password' => ['输入的密码错误.'],
//            ]);
//        }
        // token
        $expireAt = now()->addDays(30);
        $token = $user->createToken('api', ['*'], $expireAt);
        return JsonResource::make([
            'token' => $token->plainTextToken,
            'expire_at' => $expireAt
        ]);
    }


}
