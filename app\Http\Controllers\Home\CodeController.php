<?php
namespace App\Http\Controllers\Home;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redis;
use Exception;
use App\Http\Controllers\CosPhpController;
use App\Services\IpLocationService;
use App\Services\EmailNotificationService;
use Illuminate\Support\Facades\Log;
class CodeController extends Controller
{
    protected $account = 'OT00724';
    protected $password = 'anwd87v4';

    public function sendCode($info)//发送验证码
    {

        $phone = $info['phone'];
        $device = $info['devicename'];
        $contents = $info['contents'];

        $content="【蹄伺令(青岛)创新科技有限公司】您的".$device."：".$contents.",请您及时处理";
        $body=[
                'action'=>'send',
                'account'=>$this->account,
                'password'=>$this->password,
                'mobile'=>$phone,
                'content'=>$content,                
        ];
        // dd($body);
        $url = 'https://dx.ipyy.net/smsJson.aspx';

        $res = $this->httpRequest($url,$body);
        return response()->json(['result'=>1,'message'=>'成功']);
    }

    function httpRequest($url, $postData=[])
    {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        if(!empty($postData)){
            // 设置POST请求的选项
            curl_setopt($ch, CURLOPT_POST, true);
            // 设置POST请求传递的数据
            curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
        }
        // 注意:如果发送https协议请求，禁用环境的的SSL证书认证。
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);
        // 设置请求头信息
        $header = ['Accept-Charset: utf-8'];
        curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
        $result = curl_exec($ch);
        curl_close($ch);
        return $result;
    }
    public function sendNotics($phone,$contents)
    {

        $content="【科贝美】补货通知:".$contents;
        $body=[
                'action'=>'send',
                'account'=>$this->account,
                'password'=>$this->password,
                'mobile'=>$phone,
                'content'=>$content,
        ];
        $url = 'https://dx.ipyy.net/smsJson.aspx';

        $res = httpRequest($url,$body);
        $res = json_decode($res);
        return ['result'=>1,'message'=>'成功'];
    }

    /**
     * 智能通知方法 - 根据IP地址判断发送短信还是邮件
     * @param array $info 包含phone, email, devicename, contents的数组
     * @param string $clientIp 客户端IP地址（可选）
     * @return array
     */
    public function sendSmartNotification($info, $clientIp = null)
    {
        $ipLocationService = new IpLocationService();
        $emailService = new EmailNotificationService();

        // 获取用户IP地址
        $userIp = $clientIp ?: $ipLocationService->getClientIp();

        // 判断是否为中国大陆IP
        $isMainlandChina = $ipLocationService->isMainlandChina($userIp);

        // 记录IP地理位置信息
        $locationInfo = $ipLocationService->getLocationInfo($userIp);
        Log::info('用户地理位置信息', [
            'ip' => $userIp,
            'location' => $locationInfo,
            'is_mainland_china' => $isMainlandChina
        ]);

        if ($isMainlandChina) {
            // 中国大陆IP，发送短信
            return $this->sendCode($info);
        } else {
            // 非中国大陆IP，发送邮件
            if (isset($info['email']) && $emailService->isValidEmail($info['email'])) {
                $success = $emailService->sendDeviceAlert(
                    $info['email'],
                    $info['devicename'],
                    $info['contents']
                );

                if ($success) {
                    return response()->json(['result' => 1, 'message' => '邮件发送成功']);
                } else {
                    // 邮件发送失败，尝试发送短信作为备用方案
                    Log::warning('邮件发送失败，尝试发送短信作为备用方案', $info);
                    return $this->sendCode($info);
                }
            } else {
                // 没有有效邮箱，发送短信
                Log::warning('没有有效邮箱地址，使用短信发送', $info);
                return $this->sendCode($info);
            }
        }
    }


}