<?php

namespace App\Http\Controllers;

use App\Enums\ErrorCode;
use App\Exceptions\DataException;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\ValidationException;

class TestController extends Controller
{
    //

    public function test(Request $request): JsonResource
    {

        // 接口return结构
        //1. 单个资源/模型数据
        $user = Auth::user();
        return JsonResource::make($user);
        //2.1 资源列表
        $users = User::query()->get();
        return JsonResource::collection($users);
        //2.2 数组列表
        $users = [
            ['test'=>1],
            ['test'=>2],
        ];
        return JsonResource::collection($users);
        // 3. 资源or数组+额外值
        $user = ['test' => 001];
        $other = ['add_param' => 002];
        return JsonResource::make($user)->additional($other);


        // 4.1 异常-表单验证类
        // 携带key
        throw ValidationException::withMessages([ 'password' => ['输入的密码错误.'],]);
        // 4.2 异常-自定义其他 , errorcode 可以不传
        throw new DataException("测试报错",ErrorCode::DataError);
    }
}
