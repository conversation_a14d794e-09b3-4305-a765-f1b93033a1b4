<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>邮件通知系统测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            color: #2c3e50;
            margin-top: 0;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"], input[type="email"], textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #3498db;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background-color: #2980b9;
        }
        .result {
            margin-top: 15px;
            padding: 10px;
            border-radius: 4px;
            display: none;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>智能邮件通知系统测试</h1>
        
        <!-- 邮件配置测试 -->
        <div class="test-section">
            <h3>1. 邮件配置检查</h3>
            <button onclick="checkMailConfig()">检查邮件配置</button>
            <div id="config-result" class="result"></div>
        </div>

        <!-- 测试邮件发送 -->
        <div class="test-section">
            <h3>2. 测试邮件发送</h3>
            <div class="form-group">
                <label for="test-email">邮箱地址:</label>
                <input type="email" id="test-email" placeholder="请输入测试邮箱地址">
            </div>
            <button onclick="sendTestEmail()">发送测试邮件</button>
            <div id="email-result" class="result"></div>
        </div>

        <!-- 设备报警邮件测试 -->
        <div class="test-section">
            <h3>3. 设备报警邮件测试</h3>
            <div class="form-group">
                <label for="alert-email">邮箱地址:</label>
                <input type="email" id="alert-email" placeholder="请输入邮箱地址">
            </div>
            <div class="form-group">
                <label for="device-name">设备名称:</label>
                <input type="text" id="device-name" value="TEST_DEVICE_001">
            </div>
            <div class="form-group">
                <label for="alert-content">报警内容:</label>
                <textarea id="alert-content" rows="3">设备温度过高，请及时检查</textarea>
            </div>
            <button onclick="sendDeviceAlert()">发送设备报警邮件</button>
            <div id="alert-result" class="result"></div>
        </div>

        <!-- IP地理位置测试 -->
        <div class="test-section">
            <h3>4. IP地理位置检测测试</h3>
            <div class="form-group">
                <label for="test-ip">IP地址 (留空使用当前IP):</label>
                <input type="text" id="test-ip" placeholder="例如: *******">
            </div>
            <button onclick="testIpLocation()">检测IP地理位置</button>
            <div id="ip-result" class="result"></div>
        </div>

        <!-- 智能通知测试 -->
        <div class="test-section">
            <h3>5. 智能通知测试</h3>
            <div class="form-group">
                <label for="smart-phone">手机号:</label>
                <input type="text" id="smart-phone" value="13800138000">
            </div>
            <div class="form-group">
                <label for="smart-email">邮箱地址:</label>
                <input type="email" id="smart-email" placeholder="请输入邮箱地址">
            </div>
            <div class="form-group">
                <label for="smart-device">设备名称:</label>
                <input type="text" id="smart-device" value="TEST_DEVICE_001">
            </div>
            <div class="form-group">
                <label for="smart-content">报警内容:</label>
                <textarea id="smart-content" rows="3">水泵故障，请及时处理</textarea>
            </div>
            <div class="form-group">
                <label for="smart-test-ip">测试IP (留空使用当前IP):</label>
                <input type="text" id="smart-test-ip" placeholder="例如: ******* (海外IP测试邮件)">
            </div>
            <button onclick="testSmartNotification()">测试智能通知</button>
            <div id="smart-result" class="result"></div>
        </div>
    </div>

    <script>
        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.className = `result ${type}`;
            element.innerHTML = message;
            element.style.display = 'block';
        }

        function checkMailConfig() {
            fetch('/test/mail-config')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const config = data.config;
                        const message = `
                            <strong>邮件配置信息:</strong><br>
                            驱动: ${config.driver}<br>
                            主机: ${config.host}<br>
                            端口: ${config.port}<br>
                            加密: ${config.encryption}<br>
                            发件人: ${config.from_address}<br>
                            发件人名称: ${config.from_name}
                        `;
                        showResult('config-result', message, 'success');
                    } else {
                        showResult('config-result', '获取配置失败', 'error');
                    }
                })
                .catch(error => {
                    showResult('config-result', '请求失败: ' + error.message, 'error');
                });
        }

        function sendTestEmail() {
            const email = document.getElementById('test-email').value;
            if (!email) {
                showResult('email-result', '请输入邮箱地址', 'error');
                return;
            }

            fetch('/test/email', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ email: email })
            })
            .then(response => response.json())
            .then(data => {
                const type = data.success ? 'success' : 'error';
                showResult('email-result', data.message, type);
            })
            .catch(error => {
                showResult('email-result', '请求失败: ' + error.message, 'error');
            });
        }

        function sendDeviceAlert() {
            const email = document.getElementById('alert-email').value;
            const deviceName = document.getElementById('device-name').value;
            const alertContent = document.getElementById('alert-content').value;

            if (!email) {
                showResult('alert-result', '请输入邮箱地址', 'error');
                return;
            }

            fetch('/test/device-alert', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    email: email,
                    device_name: deviceName,
                    alert_content: alertContent
                })
            })
            .then(response => response.json())
            .then(data => {
                const type = data.success ? 'success' : 'error';
                showResult('alert-result', data.message, type);
            })
            .catch(error => {
                showResult('alert-result', '请求失败: ' + error.message, 'error');
            });
        }

        function testIpLocation() {
            const ip = document.getElementById('test-ip').value;
            const url = ip ? `/test/ip-location?ip=${ip}` : '/test/ip-location';

            fetch(url)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const info = data.data;
                        const message = `
                            <strong>IP地理位置信息:</strong><br>
                            IP地址: ${info.ip}<br>
                            是否中国大陆: ${info.is_mainland_china ? '是' : '否'}<br>
                            国家: ${info.location_info.country}<br>
                            地区: ${info.location_info.region}<br>
                            城市: ${info.location_info.city}<br>
                            <strong>通知方式: ${info.notification_method}</strong>
                        `;
                        showResult('ip-result', message, 'success');
                    } else {
                        showResult('ip-result', '检测失败', 'error');
                    }
                })
                .catch(error => {
                    showResult('ip-result', '请求失败: ' + error.message, 'error');
                });
        }

        function testSmartNotification() {
            const phone = document.getElementById('smart-phone').value;
            const email = document.getElementById('smart-email').value;
            const deviceName = document.getElementById('smart-device').value;
            const alertContent = document.getElementById('smart-content').value;
            const testIp = document.getElementById('smart-test-ip').value;

            if (!email) {
                showResult('smart-result', '请输入邮箱地址', 'error');
                return;
            }

            const requestData = {
                phone: phone,
                email: email,
                device_name: deviceName,
                alert_content: alertContent
            };

            if (testIp) {
                requestData.test_ip = testIp;
            }

            fetch('/test/smart-notification', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestData)
            })
            .then(response => response.json())
            .then(data => {
                const type = data.success ? 'success' : 'error';
                let message = data.message;
                if (data.result) {
                    message += '<br>详细结果: ' + JSON.stringify(data.result);
                }
                showResult('smart-result', message, type);
            })
            .catch(error => {
                showResult('smart-result', '请求失败: ' + error.message, 'error');
            });
        }
    </script>
</body>
</html>
