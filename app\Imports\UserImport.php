<?php

namespace App\Imports;

use App\Exceptions\DataException;
use App\Models\Enums\UserSex;
use App\Models\MatchCenter;
use App\Models\MatchCenterProject;
use App\Models\User;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\SkipsEmptyRows;
use Maatwebsite\Excel\Concerns\ToArray;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Imports\HeadingRowFormatter;

class UserImport implements ToArray, WithHeadingRow, SkipsEmptyRows
{
    protected static array $header = [
        "姓名" => "name",
        "队伍名称" => "team_name",
        "证件类型" => "card_type",
        "身份证/护照" => "id_card",
        "性别" => "sex",
        "参与项目" => "match_center_project_id",
        "组别名称" => "match_center_group_id",
        "报名级别" => "match_center_level_id",
        "费用" => "registration_fee",
        "缴费状态" => "payment_at",
    ];

    protected array $errors = [];

    public function sheets(): array
    {
        return [
            0 => $this//用于导入第一个 sheet
        ];
    }

    public function __construct(public MatchCenter $matchCenter)
    {
        // 重构标题
        HeadingRowFormatter::extend('slug', function ($value, $key) {
            return Arr::get(static::$header, $value);
        });
    }

    public function array(array $array)
    {
        if (empty($array)) {
            return false;
        }
        $projects = $this->matchCenter->projects()
            ->select(['project', 'id'])
            ->with([
                'groups:id,name,match_center_project_id',
                'groups.levels:id,level,match_center_group_id',
            ])
            ->get()->keyBy('project');
        foreach ($array as $key => $row) {
            try {
                $project = $projects->get($row['match_center_project_id']);
                if (!$project) {
                    throw new DataException("项目不存在");
                }
                $row['match_center_project_id'] = $project->id;
                $group = $project->groups->where('name', $row['match_center_group_id'])->first();
                if (!$group) {
                    throw new DataException("组别不存在");
                }
                $row['match_center_group_id'] = $group->id;
                $level = $group->levels->where('level', $row['match_center_level_id'])->first();
                if (!$level) {
                    throw new DataException("报名级别不存在");
                }
                $row['match_center_level_id'] = $level->id;
                if ($row['payment_at'] == '是') {
                    $row['payment_at'] = now();
                } else {
                    $row['payment_at'] = null;
                }
                if ($row['card_type'] == '身份证') {
                    $row['card_type'] = 1;
                } else {
                    $row['card_type'] = 2;
                }
                if ($row['sex'] == '男') {
                    $row['sex'] = UserSex::Male;
                } else {
                    $row['sex'] = UserSex::Female;
                }
                $row['match_center_id'] = $this->matchCenter->id;
                $user = new User();
                $user->fill($row)->save();

            } catch (\Throwable $e) {
                $line = $key + 2;
                $this->errors[] = "第{$line}行导入失败,错误信息:{$e->getMessage()}";
            }

        }
        return true;
    }

    public function getErrors()
    {
        return $this->errors;
    }
}
