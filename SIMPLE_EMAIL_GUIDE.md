# 智能邮件通知功能 - 简化指南

## 功能说明

系统现在可以根据用户IP地址智能选择通知方式：
- **中国大陆IP** → 发送短信
- **海外IP** → 发送邮件（纯文本格式）
- **邮件失败** → 自动回退到短信

## 快速设置

### 1. 数据库设置
```bash
php artisan migrate
```
这会给 `plc_user` 表添加 `email` 字段。

### 2. Gmail配置
在 `.env` 文件中添加：
```env
MAIL_DRIVER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-16-digit-app-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="设备监控系统"
```

**重要**：
- 必须使用Gmail的16位应用密码，不是登录密码
- 需要先启用Gmail的两步验证

### 3. 测试功能
访问：`http://your-domain/email-test`

## 测试API

所有测试API都在 `/api/test/` 路径下，避免CSRF问题：

1. **邮件配置检查**：`GET /api/test/mail-config`
2. **测试邮件**：`POST /api/test/email`
3. **设备报警邮件**：`POST /api/test/device-alert`
4. **IP检测**：`GET /api/test/ip-location`
5. **智能通知**：`POST /api/test/smart-notification`

## 邮件内容示例

发送的纯文本邮件格式：
```
【蹄伺令(青岛)创新科技有限公司】

您的设备出现了需要关注的情况，请及时处理。

设备编号：DEVICE_001
报警内容：水泵故障

报警时间：2024-01-01 12:00:00

请您及时登录系统查看详细信息并处理相关问题。

此邮件为系统自动发送，请勿回复。
```

## 如何使用

原有的通知代码已自动升级，无需修改。系统会自动：
1. 检测用户IP地址
2. 判断是否为中国大陆
3. 选择合适的通知方式
4. 记录详细日志

## 常见问题

**Q: 邮件发送失败怎么办？**
A: 系统会自动回退到短信通知，并记录错误日志。

**Q: 如何修改邮件内容？**
A: 编辑 `app/Services/EmailNotificationService.php` 中的 `sendDeviceAlert` 方法。

**Q: 如何查看日志？**
A: 查看 `storage/logs/laravel.log` 文件。

**Q: Gmail应用密码在哪里获取？**
A: Google账户 → 安全性 → 两步验证 → 应用密码

## 文件说明

**新增文件**：
- `app/Services/IpLocationService.php` - IP检测服务
- `app/Services/EmailNotificationService.php` - 邮件服务
- `app/Http/Controllers/Api/EmailTestController.php` - 测试控制器
- `resources/views/email_test.blade.php` - 测试页面

**修改文件**：
- `app/Http/Controllers/Api/MqttController.php` - 集成智能通知
- `app/Http/Controllers/Home/CodeController.php` - 添加智能通知方法

就这么简单！系统现在可以智能地为海外用户发送邮件通知了。
