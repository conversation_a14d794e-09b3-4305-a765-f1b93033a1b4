<?php

namespace App\Http\Resources;

use App\Models\OperationLog;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class OperationLogResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        /**
         * @var OperationLog $resource
         */
        $resource = $this->resource;
        $role = $resource->adminUser?->roles->first();
        return [
            'id' => $resource->id,
            'ip' => $resource->ip,
            'content' => $resource->content,
            'account' => $resource->adminUser->account,
            'name' => $resource->adminUser->name,
            'role' => $role->name,
            'role_name' => $role->name_zh,
            'created_at' => $resource->created_at,
        ];
    }
}
