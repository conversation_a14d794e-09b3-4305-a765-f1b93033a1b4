<?php

function objToArr($obj)
{
    $data = json_decode(json_encode($obj), true);
    return $data;
}

function panduanUser($string)
{
    if (preg_match('/[a-zA-Z0-9]{5,10}/', $string)) {
        return 1;
    } else {
        return 0;
    }
}

function panduanPwd($pwd)
{
    if (preg_match('/\w{8,16}/', $pwd)) {
        return 1;
    } else {
        return 0;
    }
}

function panduanPhone($phone)
{
    if (preg_match('/^1((34[0-8]\d{7})|((3[0-3|5-9])|(4[5-7|9])|(5[0-3|5-9])|(66)|(7[2-3|5-8])|(8[0-9])|(9[1|8|9]))\d{8})$/', $phone)) {
        return 1;
    } else {
        return 0;
    }
}

function jieguo($code, $message)
{
    return json_encode(['result' => $code, 'message' => $message]);
}

function jieguo1($code, $message, $data)
{
    return json_encode(['result' => $code, 'message' => $message, 'data' => $data['data'], 'total' => $data['total'], 'page' => $data['last_page']]);
}


function crc16($string)
{
    $crc = 0xFFFF;
    for ($x = 0; $x < strlen($string); $x++) {
        $crc = $crc ^ ord($string[$x]);
        for ($y = 0; $y < 8; $y++) {
            if (($crc & 0x0001) == 0x0001) {
                $crc = (($crc >> 1) ^ 0xA001);
            } else {
                $crc = $crc >> 1;
            }
        }
    }
    return $crc;
}

//转成2字节
function zhuanhuan($id)
{
    $id = intval($id);
    $a[0] = $id >> 8 & 0xff;
    $a[1] = $id & 0xff;
    return $a;
}


//字符串转数组(16进制前面加dechx)
function getBytes($string)
{
    $bytes = array();
    for ($i = 0; $i < strlen($string); $i++) {
        $bytes[] = ord($string[$i]);
    }
    // $hex = array_slice($bytes,4,12);

    return $bytes;
}

function hexToStr($hex)
{
    $str = "";
    for ($i = 0; $i < strlen($hex) - 1; $i += 2) {
        $str .= chr(hexdec($hex[$i] . $hex[$i + 1]));
    }
    return $str;
}

function zhuanInt($id)
{
    $id = intval($id);
    $a[0] = $id >> 24 & 0xff;
    $a[1] = $id >> 16 & 0xff;
    $a[2] = $id >> 8 & 0xff;
    $a[3] = $id & 0xff;
    return $a;
}

function decToDecFloat($strDec)
{
    $v = hexdec($strDec);
    $x = ($v & ((1 << 23) - 1)) + (1 << 23) * ($v >> 31 | 1);
    $exp = ($v >> 23 & 0xFF) - 127;
    $a = $x * pow(2, $exp - 23);
    if ($v >> 31) {
        $a *= -1;
    }
    return $a;
}

function writeFile($message, $txt)
{
    file_put_contents("/www/wwwroot/api/storage/logs/" . $txt, json_encode($message) . PHP_EOL, FILE_APPEND);
}

//加密
function encrypts($data, $key = 'plc', $char = '', $str = '')
{
    $key = md5($key);
    $x = 0;
    $len = strlen($data);
    $l = strlen($key);
    for ($i = 0; $i < $len; $i++) {
        if ($x == $l) {
            $x = 0;
        }
        $char .= $key{$x};
        $x++;
    }
    for ($i = 0; $i < $len; $i++) {
        $str .= chr(ord($data{$i}) + (ord($char{$i})) % 256);
    }
    return base64_encode($str);
}

//解密
function decrypts($data, $key = 'plc', $char = '', $str = '')
{
    $key = md5($key);
    $x = 0;
    $data = base64_decode($data);
    $len = strlen($data);
    $l = strlen($key);
    for ($i = 0; $i < $len; $i++) {
        if ($x == $l) {
            $x = 0;
        }
        $char .= substr($key, $x, 1);
        $x++;
    }
    for ($i = 0; $i < $len; $i++) {
        if (ord(substr($data, $i, 1)) < ord(substr($char, $i, 1))) {
            $str .= chr((ord(substr($data, $i, 1)) + 256) - ord(substr($char, $i, 1)));
        } else {
            $str .= chr(ord(substr($data, $i, 1)) - ord(substr($char, $i, 1)));
        }
    }
    return $str;
}


function httpRequest($url, $postData = [])
{
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    if (!empty($postData)) {
        // 设置POST请求的选项
        curl_setopt($ch, CURLOPT_POST, true);
        // 设置POST请求传递的数据
        curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
    }
    // 注意:如果发送https协议请求，禁用环境的的SSL证书认证。
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);
    // 设置请求头信息
    $header = ['Accept-Charset: utf-8'];
    curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
    $result = curl_exec($ch);
    curl_close($ch);
    return $result;
}

function page_array($count, $page, $array)
{
    global $countpage; #定全局变量
    $page = (empty($page)) ? '1' : $page; #判断当前页面是否为空 如果为空就表示为第一页面
    $start = ($page - 1) * $count; #计算每次分页的开始位置
    $totals = count($array);
    $countpage = ceil($totals / $count); #计算总页面数
    $pagedata = array();
    $pagedata = array_slice($array, $start, $count);
    return ['data' => $pagedata, 'count' => $totals, 'page' => $countpage];
}