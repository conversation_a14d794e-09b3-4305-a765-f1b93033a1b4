<?php

namespace App\Http\Controllers;

use App\Exceptions\DataException;
use App\Http\Resources\MatchCenterResource;
use App\Models\AdminUser;
use App\Models\Enums\MatchCenterEnrollType;
use App\Models\Enums\MatchCenterLevel;
use App\Models\Enums\MatchCenterStatus;
use App\Models\MatchCenter;
use App\Models\MatchDetail;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rules\Enum;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\QueryBuilder;

class MatchCenterController extends Controller
{
    //

    /**
     * 设置场地
     * @param Request $request
     * @param MatchCenter $matchCenter
     * @return AnonymousResourceCollection
     * @throws DataException
     */
    public function setSite(Request $request,MatchCenter $matchCenter): AnonymousResourceCollection
    {
        $validated = $request->validate([
            'site'=>'string|required',
            'ids'=>'array',
        ]);
        $ids = $validated['ids'];
        $site = $validated['site'];
        $details = MatchDetail::query()
            ->with(['schedule'])
            ->whereIn('id',$ids)
            ->get();
        foreach ($details as $detail) {
            if ($detail->schedule){
                $detail->schedule->update([
                    'venue'=>$site
                ]);
            }else{
               throw new DataException("请先分配比赛日期");
            }
        }
        return JsonResource::collection([]);
    }

    public function details(Request $request,MatchCenter $matchCenter): AnonymousResourceCollection
    {
        $builder = QueryBuilder::for(MatchDetail::class,$request)
            ->where('match_center_id', $matchCenter->id)
            ->with([
                'matchCenterGroupLevel:id,level',
                'matchCenterGroup:id,name',
                'matchCenterProject:id,project',
                'schedule',
                'referee1',
                'referee2',
                'referee3',
                'referee4',
                'refereeT',
            ])
            ->allowedFilters([
                AllowedFilter::exact('match_day','schedule.day'),
                AllowedFilter::exact('match_time','schedule.time'),
                AllowedFilter::exact('site','schedule.venue'),
                AllowedFilter::exact('match_center_level_id'),
                AllowedFilter::exact('match_center_group_id'),
                AllowedFilter::exact('match_center_project_id'),
                AllowedFilter::exact('step'),
            ])
            ->defaultSort('-id');
        $list = $builder->paginate($this->getPerPage());
        foreach ($list->items() as $item) {
            $item->red = matchService()->getCenterUser($item,1);
            $item->blue = matchService()->getCenterUser($item,2);
        }

        return JsonResource::collection($list);

    }



    /**
     * @param Request $request
     * @return AnonymousResourceCollection
     */
    public function index(Request $request): AnonymousResourceCollection
    {
        $builder = QueryBuilder::for(MatchCenter::class)
            ->select([
                'id', 'create_user_id', 'title', 'desc', 'created_at'
            ])
            ->with([
                'createUser:id,name'
            ])
            ->allowedFilters([
                AllowedFilter::partial('title'),
                AllowedFilter::partial('create_user_name', 'createUser.name'),
                AllowedFilter::scope('created_at_between'),
            ])
            ->defaultSort('-id');

        return JsonResource::collection($builder->paginate($this->getPerPage()));
    }

    /**
     * 详情
     * @param Request $request
     * @param MatchCenter $matchCenter
     * @return MatchCenterResource
     */
    public function show(Request $request, MatchCenter $matchCenter): MatchCenterResource
    {
        $matchCenter->loadMissing([
            'projects:id,id as projectId,match_center_id,project,project as projectName',
            'projects.groups:id,id as groupId,name,name as groupName,match_center_project_id',
            'projects.groups.levels:id,id as levelId,level as levelName,match_center_group_id,level,sex',
        ]);
        return MatchCenterResource::make($matchCenter);
    }

    /**
     * 创建赛事
     * @param Request $request
     * @param MatchCenter $matchCenter
     * @return JsonResource
     */
    public function store(Request $request, MatchCenter $matchCenter): JsonResource
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
        ]);
        /**
         * @var AdminUser $user
         */
        $user = Auth::user();
        $matchCenter->fill([
            ...$validated,
            'status' => MatchCenterStatus::Enrolling
        ]);
        $matchCenter->createUser()->associate($user)->save();
        return JsonResource::make($matchCenter);
    }

    /**
     * 编辑
     * @param Request $request
     * @param MatchCenter $matchCenter
     * @return JsonResource
     */
    public function update(Request $request, MatchCenter $matchCenter): JsonResource
    {
        $validated = $request->validate([
            'title' => 'string|max:255',
            'desc' => 'nullable|string',
            'logo_url' => 'nullable|string|url|max:255',
            'placard_url' => 'nullable|string|url|max:255',
            'status' => [new Enum(MatchCenterStatus::class)],
            'level' => [new Enum(MatchCenterLevel::class)],
            'enroll_start_date' => 'nullable|date',
            'enroll_end_date' => 'nullable|date',
            'compete_start_date' => 'nullable|date',
            'compete_end_date' => 'nullable|date',
            'address' => 'nullable|string|max:255',
            'sponsor_company' => 'nullable|string|max:255',
            'sponsor_phone' => 'nullable|string|max:255',
            'guide_company' => 'nullable|string|max:255',
            'organizer_company' => 'nullable|string|max:255',
            'contest_regulations_url' => 'nullable|string|url|max:255',
            'participation_certificates_url' => 'nullable|string|url|max:255',
            'other_certificates_urls' => 'nullable|array',
            'other_certificates_urls.*' => 'nullable|string|url',
            'program_url' => 'nullable|string|url|max:255',
            'enroll_type' => [new Enum(MatchCenterEnrollType::class)],
            'sites' => 'nullable|array',
        ]);
        $matchCenter->update($validated);
        return JsonResource::make($matchCenter);
    }

    /**
     * 删除赛事
     * @param Request $request
     * @param MatchCenter $matchCenter
     * @return JsonResource
     */
    public function destroy(Request $request, MatchCenter $matchCenter): JsonResource
    {
        $matchCenter->delete();
        return JsonResource::make([]);
    }
}
