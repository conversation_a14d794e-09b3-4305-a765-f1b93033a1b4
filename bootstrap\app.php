<?php

use App\Enums\ErrorCode;
use App\Exceptions\DataException;
use App\Http\Middleware\AcceptHeader;
use App\Http\Middleware\Authenticate;
use App\Http\Middleware\JsonResponseCode;
use App\Http\Middleware\UserCheck;
use App\Http\Middleware\AdminCheck;
use Illuminate\Auth\AuthenticationException;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Database\QueryException;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;
use Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull;
use Illuminate\Http\Middleware\HandleCors;
use Illuminate\Http\Request;
use Symfony\Component\HttpKernel\Exception\MethodNotAllowedHttpException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__ . '/../routes/web.php',
        api: __DIR__ . '/../routes/api.php',
        commands: __DIR__ . '/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware): void {
        $middleware->remove(ConvertEmptyStringsToNull::class);
        $middleware->api([
            AcceptHeader::class,
            JsonResponseCode::class,
            HandleCors::class,
        ]);
        $middleware->alias([
            'auth' => Authenticate::class,
            'user.check' => UserCheck::class,
            'admin.check' => AdminCheck::class,
        ]);

    })
    ->withExceptions(function (Exceptions $exceptions): void {
        $exceptions->render(function (Throwable $e, Request $request) {
            // 正式环境需要去敏感
            if (!config('app.debug')) {
                if ($e instanceof QueryException) {
                    throw new DataException("数据库连接错误", ErrorCode::DataError, $e);
                }
            }
            switch (true) {
                case $e instanceof NotFoundHttpException:
                case $e instanceof ModelNotFoundException:
                    throw new DataException("访问路由不存在", ErrorCode::HttpNotFound);
                case $e instanceof MethodNotAllowedHttpException:
                    throw new DataException("访问路由方式错误", ErrorCode::HttpMethodNotFound);
                case $e instanceof AuthenticationException:
                    throw new DataException("用户登录token无效或已过期", ErrorCode::Unauthenticated);
                default:
                    break;
            }
        });
        $exceptions->renderable(function (DataException $e, Request $request) {
            return response()->json([
                'message' => $e->getMessage(),
                'code' => $e->getErrorCode()
            ]);
        });
        //
    })->create();
