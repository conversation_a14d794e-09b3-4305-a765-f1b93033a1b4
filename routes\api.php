<?php

use App\Http\Controllers\AdminUserController;
use App\Http\Controllers\Api\CameraController;
use App\Http\Controllers\Api\VenueMachineController;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\MatchCenterController;
use App\Http\Controllers\MatchCenterProjectController;
use App\Http\Controllers\OperationLogController;
use App\Http\Controllers\RefereeController;
use App\Http\Controllers\TestController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\RoleController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;


## 后端不登录
Route::middleware([])->group(function () {
    Route::get('/test', [TestController::class, 'test']);

    ## AUTH模块
    Route::middleware([])->group(function () {
        Route::post('/auth/login', [AuthController::class, 'login']);
    });

    ## 场地机系统接口
    Route::prefix('venue-machines')->group(function () {
        // 场地机上报接口（供场地机系统调用）
        Route::post('/report', [VenueMachineController::class, 'report']);
    });

    ## 摄像机系统接口
    Route::prefix('cameras')->group(function () {
        // 摄像机上报接口（供摄像机系统调用）
        Route::post('/report', [CameraController::class, 'report']);
    });
});


## 后端登录
Route::middleware(['auth:sanctum', 'admin.check'])->group(function () {
    ## 个人中心
    Route::group([], function () {
        // 个人中心
        Route::get('/admin/info', [AdminUserController::class, 'info']);
        // 修改姓名
        Route::post('/admin/update-name', [AdminUserController::class, 'updateName']);
        // 修改密码
        Route::post('/admin/update-password', [AdminUserController::class, 'updatePassword']);
    });

    ## 赛事中心
    Route::group([], function () {

        // 赛事中心列表
        Route::post('/match-centers/_search', [MatchCenterController::class, 'index']);
        // 新增赛事
        Route::post('/match-centers', [MatchCenterController::class, 'store']);
        // 赛事详情
        Route::get('/match-centers/{matchCenter}', [MatchCenterController::class, 'show']);
        // 赛事更新/修改
        Route::put('/match-centers/{matchCenter}', [MatchCenterController::class, 'update']);
        // 删除赛事
        Route::delete('/match-centers/{matchCenter}', [MatchCenterController::class, 'destroy']);

        ### 赛事中心-比赛项目
        Route::group([], function () {
            // 项目列表
            Route::post('/match-centers/{matchCenter}/projects/_search', [MatchCenterProjectController::class, 'index']);
            // 创建项目
            Route::post('/match-centers/{matchCenter}/projects', [MatchCenterProjectController::class, 'store']);
            // 修改项目
            Route::put('/match-centers/{matchCenter}/projects/{project}', [MatchCenterProjectController::class, 'update']);
            // 删除项目
            Route::delete('/match-centers/{matchCenter}/projects/{project}', [MatchCenterProjectController::class, 'destroy']);

            // 组别列表
            Route::post('/match-center-projects/{project}/groups/_search', [MatchCenterProjectController::class, 'groupIndex']);
            // 新增组别
            Route::post('/match-center-projects/{project}/groups', [MatchCenterProjectController::class, 'groupStore']);
            // 删除组别
            Route::delete('/match-center-projects/{project}/groups/{group}', [MatchCenterProjectController::class, 'groupDestroy']);
            // 修改组别
            Route::put('/match-center-projects/{project}/groups/{group}', [MatchCenterProjectController::class, 'groupUpdate']);
            // 快速分级
            Route::post('/match-center-projects/{project}/groups/{group}/fast-grading', [MatchCenterProjectController::class, 'groupFastGrading']);

            ### 赛事中心-抽签分组管理 (最小级别)
            Route::group([], function () {
                // 列表
                Route::post('/match-centers/{matchCenter}/levels/_search', [MatchCenterProjectController::class, 'levelIndex']);
                // 抽签
                Route::post('/match-centers/{matchCenter}/levels/draw', [MatchCenterProjectController::class, 'draw']);
                // 裁判抽签
                Route::post('/match-centers/{matchCenter}/levels/referee-draw', [MatchCenterProjectController::class, 'refereeDraw']);
                // 抽签详情
                Route::get('/match-centers/{matchCenter}/levels/{level}/details', [MatchCenterProjectController::class, 'levelDetails']);
                // 比赛调整修改
                Route::post('/match-centers/{matchCenter}/levels/{level}/details', [MatchCenterProjectController::class, 'levelDetailsUpdate']);
            });

            // 抽签详情-全部
            Route::post('/match-centers/{matchCenter}/level/details/_search', [MatchCenterProjectController::class, 'allLevelDetails']);

            ### 赛事中心-组别日程
            Route::group([], function () {
                // 赛事日期分配列表
                Route::post('/match-centers/{matchCenter}/level-schedules/_search', [MatchCenterProjectController::class, 'levelScheduleIndex']);
                // 赛事日期分配
                Route::post('/match-centers/{matchCenter}/level/{level}/schedules', [MatchCenterProjectController::class, 'levelScheduleStore']);
            });
            Route::post('/match-centers/{matchCenter}/details/_search', [MatchCenterController::class, 'details']);
            Route::post('/match-centers/{matchCenter}/details/_set-site', [MatchCenterController::class, 'setSite']);
        });

        ### 筛选内容快捷
        Route::group([], function () {
            // 运动员列表
            Route::get('/match-centers/{matchCenter}/project/_options', [MatchCenterProjectController::class, 'options']);
        });

        ### 赛事中心-运动员报名信息
        Route::group([], function () {
            // 运动员列表
            Route::post('/match-centers/{matchCenter}/users/_search', [UserController::class, 'index']);
            // 运动员导出 todo::
            Route::post('/match-centers/{matchCenter}/users/_export', [UserController::class, 'export']);
            // 新增报名
            Route::post('/match-centers/{matchCenter}/users', [UserController::class, 'store']);
            // 导入报名
            Route::post('/match-centers/{matchCenter}/users/import', [UserController::class, 'import']);
            // 修改用户
            Route::put('/match-centers/{matchCenter}/users/{user}', [UserController::class, 'update']);
            // 取消用户
            Route::post('/match-centers/{matchCenter}/users/{user}/channel', [UserController::class, 'channel']);
            // 注销参赛队
            Route::post('/match-centers/{matchCenter}/users/log-out', [UserController::class, 'logOut']);
            // 删除项目
            Route::delete('/match-centers/{matchCenter}/users/{user}', [UserController::class, 'destroy']);
            // 完成缴费
            Route::post('/match-centers/{matchCenter}/user/batch-payment', [UserController::class, 'batchPayment']);
            // 批量删除
            Route::post('/match-centers/{matchCenter}/user/batch-destroy', [UserController::class, 'batchDestroy']);
            // 发布到参赛
            Route::post('/match-centers/{matchCenter}/user/publish', [UserController::class, 'publish']);
        });

        ### 赛事中心-裁判
        Route::group([], function () {
            // 裁判列表
            Route::post('/match-centers/{matchCenter}/referees/_search', [RefereeController::class, 'index']);
            // 新增裁判
            Route::post('/match-centers/{matchCenter}/referees', [RefereeController::class, 'store']);
            // 修改裁判
            Route::put('/match-centers/{matchCenter}/referees/{referee}', [RefereeController::class, 'update']);
            // 删除裁判
            Route::delete('/match-centers/{matchCenter}/referees/{referee}', [RefereeController::class, 'destroy']);
        });


        ## 后台管理
        Route::group([], function () {
            // 系统账号管理
            Route::group([], function () {
                // 列表
                Route::post('admin-users/_search', [AdminUserController::class, 'index']);

                // 新增管理员
                Route::post('admin-users', [AdminUserController::class, 'store']);
                // 批量删除管理员
                Route::post('admin-users/_batch_destroy', [AdminUserController::class, 'batchDelete']);
                // 详情
                Route::get('admin-users/{user}', [AdminUserController::class, 'show']);
                // 修改
                Route::post('admin-users/{user}', [AdminUserController::class, 'update']);
                // 修改状态
                Route::put('admin-users/{user}/status', [AdminUserController::class, 'status']);
            });

            // 权限管理(角色)
            Route::group([], function () {
                // 列表
                Route::post('roles/_search', [RoleController::class, 'index']);
                // 新增角色
                Route::post('roles', [RoleController::class, 'store']);
                // 详情
                Route::get('roles/{role}', [RoleController::class, 'show']);
                // 修改
                Route::put('roles/{role}', [RoleController::class, 'update']);
                // 批量删除角色
                Route::post('roles/_batch_destroy', [RoleController::class, 'batchDelete']);
                // 权限列表
                Route::get('permissions', [RoleController::class, 'permissions']);
            });

            // 场地机管理
            Route::prefix('venue-machines')->group(function () {
                // 场地机列表（管理后台）
                Route::post('/_search', [VenueMachineController::class, 'index']);
                // 编辑场地机信息
                Route::put('/{venueMachine}', [VenueMachineController::class, 'update']);
            });

            // 摄像机管理
            Route::prefix('cameras')->group(function () {
                // 摄像机列表（管理后台）
                Route::post('/_search', [CameraController::class, 'index']);
                // 编辑摄像机信息
                Route::put('/{camera}', [CameraController::class, 'update']);
            });
        });
    });

    ## 操作日志
    Route::post('/operation-logs/_search', [OperationLogController::class, 'index']);
    // 上传文件
    Route::post('/upload', [AdminUserController::class, 'upload']);
});

## 小程序接口
Route::prefix('mini-program')->group(base_path('routes/mini_program.php'));
