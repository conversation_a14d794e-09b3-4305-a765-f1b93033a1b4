<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\VenueMachine;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\JsonResource;
use Carbon\Carbon;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\QueryBuilder;
use Exception;

class VenueMachineController extends Controller
{
    /**
     * 场地机上报接口
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function report(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'venue_number' => 'required|string|max:50',
                'machine_number' => 'required|string|max:100',
                'remarks' => 'nullable|string'
            ]);

            $venueNumber = $validated['venue_number'];
            $machineNumber = $validated['machine_number'];
            $remarks = $validated['remarks'];
            $now = Carbon::now();

            // 使用 Spatie QueryBuilder 查找现有记录
            $existingMachine = QueryBuilder::for(VenueMachine::class)
                ->where('machine_number', $machineNumber)
                ->first();

            $isNew = false;
            $venueMachine = null;

            if ($existingMachine) {
                // 更新现有记录
                $existingMachine->update([
                    'venue_number' => $venueNumber,
                    'remarks' => $remarks,
                    'last_report_time' => $now,
                ]);
                $venueMachine = $existingMachine->fresh();
            } else {
                // 创建新记录
                $venueMachine = VenueMachine::create([
                    'venue_number' => $venueNumber,
                    'machine_number' => $machineNumber,
                    'remarks' => $remarks,
                    'last_report_time' => $now,
                ]);
                $isNew = true;
            }

            return response()->json([
                'success' => true,
                'message' => $isNew ? '场地机注册成功' : '场地机上报时间更新成功',
                'data' => [
                    'id' => $venueMachine->id,
                    'venue_number' => $venueMachine->venue_number,
                    'machine_number' => $venueMachine->machine_number,
                    'remarks' => $venueMachine->remarks,
                    'last_report_time' => $venueMachine->last_report_time,
                    'is_new' => $isNew,
                ],
            ], 200);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '服务器内部错误',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * 获取场地机列表
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        try {
            // 使用 Spatie QueryBuilder 处理查询和筛选
            $venueMachines = QueryBuilder::for(VenueMachine::class)
                ->allowedFilters([
                    AllowedFilter::partial('venue_number'),     // 支持场地号模糊查找
                    AllowedFilter::partial('machine_number'),   // 支持机器编号模糊查找
                ])
                ->allowedSorts([
                    'venue_number',
                    'machine_number',
                    'last_report_time',
                    'created_at',
                    'updated_at'
                ])
                ->defaultSort('-last_report_time')  // 默认按最后上报时间降序
                ->paginate($request->input('per_page', 15));

            // 格式化数据
            $currentTime = Carbon::now();
            $data = $venueMachines->getCollection()->map(function ($machine) use ($currentTime) {
                // 计算在线状态：最后上报时间与当前时间差小于6秒为在线
                $isOnline = false;
                $status = 'offline';
                $statusText = '离线';

                if ($machine->last_report_time) {
                    $lastReportTime = Carbon::parse($machine->last_report_time);
                    $secondsDiff = $currentTime->diffInSeconds($lastReportTime);

                    if ($secondsDiff <= 6) {
                        $isOnline = true;
                        $status = 'online';
                        $statusText = '在线';
                    }
                }

                return [
                    'id' => $machine->id,
                    'venue_number' => $machine->venue_number,
                    'machine_number' => $machine->machine_number,
                    'remarks' => $machine->remarks,
                    'last_report_time' => $machine->last_report_time,
                    'created_at' => $machine->created_at,
                    'updated_at' => $machine->updated_at,
                    'is_online' => $isOnline,
                    'status' => $status,
                    'status_text' => $statusText,
                ];
            });

            return response()->json([
                'success' => true,
                'message' => '获取成功',
                'data' => $data,
                'pagination' => [
                    'current_page' => $venueMachines->currentPage(),
                    'last_page' => $venueMachines->lastPage(),
                    'per_page' => $venueMachines->perPage(),
                    'total' => $venueMachines->total(),
                ],
            ], 200);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '服务器内部错误',
                'error' => $e->getMessage(),
            ], 500);
        }
    }


    /**
     * 编辑场地机信息
     * 
     * @param Request $request
     * @param VenueMachine $venueMachine
     * @return JsonResource
     */
    public function update(Request $request, VenueMachine $venueMachine): JsonResource
    {
        // 验证请求参数
        $validated = $request->validate([
            'venue_number' => 'required|string|max:50',
            'machine_number' => 'required|string|max:100',
            'remarks' => 'nullable|string',
        ]);

        $venueMachine->update($validated);

        return JsonResource::make($venueMachine);
    }
}
