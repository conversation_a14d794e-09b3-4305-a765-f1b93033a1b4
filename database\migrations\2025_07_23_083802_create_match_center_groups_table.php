<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('match_center_groups', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('match_center_project_id')->index()->comment('所属项目');
            $table->string('name')->comment('组别名称');
            $table->date('max_birthday_date')->comment('最大生日');
            $table->date('min_birthday_date')->comment('最小生日');
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('match_center_groups');
    }
};
