<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('match_center_projects', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('match_center_id')->index()->comment('所属项目');
            $table->string('project')->comment('项目名称/比赛项目');
            $table->tinyInteger('project_type')->comment('比赛类型');
            $table->tinyInteger('source_type')->comment('记分形式');
            $table->tinyInteger('sex_type')->comment('性别要求');
            $table->tinyInteger('enter_type')->comment('上场方式');
            $table->decimal('service_fee')->default(0)->comment('服务费');
            $table->softDeletes();
            $table->timestamps();
            $table->comment('比赛项目表');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('match_center_projects');
    }
};
