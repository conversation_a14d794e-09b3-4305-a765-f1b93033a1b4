<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class DeviceAlertMail extends Mailable
{
    use Queueable, SerializesModels;

    public $deviceName;
    public $alertContent;
    public $timestamp;

    /**
     * Create a new message instance.
     *
     * @param string $deviceName
     * @param string $alertContent
     */
    public function __construct($deviceName, $alertContent)
    {
        $this->deviceName = $deviceName;
        $this->alertContent = $alertContent;
        $this->timestamp = date('Y-m-d H:i:s');
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->subject('设备报警通知 - ' . $this->deviceName)
                    ->view('emails.device_alert')
                    ->with([
                        'deviceName' => $this->deviceName,
                        'alertContent' => $this->alertContent,
                        'timestamp' => $this->timestamp,
                    ]);
    }
}
