<?php

namespace App\Models;

use App\Api\Manage\ManageService;
use App\Models\Enums\MatchCenter\ProjectEnterType;
use App\Models\Enums\MatchCenter\ProjectType;
use App\Models\Enums\MatchCenter\ProjectSexType;
use App\Models\Enums\MatchCenter\ProjectSourceType;
use App\Models\Enums\MatchCenterEnrollType;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @property int $match_center_id
 * @property int $id
 */
class MatchCenterProject extends Model
{
    use SoftDeletes;

    //
    public $guarded = [];
    public $appends = [
    ];

    public $casts = [
        'service_fee' => 'decimal:2',
        'enter_type' => ProjectEnterType::class,
        'sex_type' => ProjectSexType::class,
        'project_type' => ProjectType::class,
        'source_type' => ProjectSourceType::class,
    ];
    protected static function booted()
    {
        static::updated(function (self $project) {
            // todo:: 做成按钮主动更新?
//            ManageService::sync2MiniProgramProjects($project->matchCenter);
        });
        static::created(function (self $project) {
//            ManageService::sync2MiniProgramProjects($project->matchCenter);
        });
    }

    public function matchCenter(): BelongsTo
    {
        return $this->belongsTo(MatchCenter::class);
    }

    public function groups(): HasMany
    {
        return $this->hasMany(MatchCenterGroup::class, 'match_center_project_id', 'id');
    }


}
