<?php

namespace App\Services;

use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;
use App\Mail\DeviceAlertMail;

class EmailNotificationService
{
    /**
     * 发送设备报警邮件
     * @param string $email 收件人邮箱
     * @param string $deviceName 设备名称
     * @param string $alertContent 报警内容
     * @return bool
     */
    public function sendDeviceAlert($email, $deviceName, $alertContent)
    {
        try {
            Mail::to($email)->send(new DeviceAlertMail($deviceName, $alertContent));
            
            Log::info("设备报警邮件发送成功", [
                'email' => $email,
                'device' => $deviceName,
                'content' => $alertContent
            ]);
            
            return true;
        } catch (\Exception $e) {
            Log::error("设备报警邮件发送失败: " . $e->getMessage(), [
                'email' => $email,
                'device' => $deviceName,
                'content' => $alertContent
            ]);
            
            return false;
        }
    }

    /**
     * 验证邮箱格式
     * @param string $email
     * @return bool
     */
    public function isValidEmail($email)
    {
        return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
    }

    /**
     * 发送测试邮件
     * @param string $email
     * @return bool
     */
    public function sendTestEmail($email)
    {
        try {
            Mail::raw('这是一封测试邮件，用于验证邮件配置是否正确。', function ($message) use ($email) {
                $message->to($email)
                        ->subject('邮件配置测试');
            });
            
            return true;
        } catch (\Exception $e) {
            Log::error("测试邮件发送失败: " . $e->getMessage());
            return false;
        }
    }
}
