<?php

namespace App\Constants;

use Illuminate\Support\Arr;

enum Permissions: string
{
    case RolesManage = 'roles.manage';

    public static function values(): array
    {
        return array_column(self::list(), 'value');
    }

    public function desc(): string
    {
        return Arr::get(self::list(), $this->value);
    }

    public static function list(): array
    {
        return [
            self::RolesManage->value => '角色管理'
        ];
    }
}
