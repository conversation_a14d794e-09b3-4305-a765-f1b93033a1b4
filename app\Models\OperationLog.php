<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Carbon;

class OperationLog extends Model
{
    //
    public $guarded = [];

    public $casts = [
        'param' => 'json'
    ];
    public $hidden = [
        'param'
    ];


    public function adminUser(): BelongsTo
    {
        return $this->belongsTo(AdminUser::class);
    }

    public function scopeCreatedAtBetween(Builder $builder, $start = null, $end = null): void
    {
        try {
            if ($start) {
                $builder->where('created_at', '>=', Carbon::parse($start));
            }
            if ($end) {
                $builder->where('created_at', '<=', Carbon::parse($end));
            }
        } catch (\Throwable $exception) {
        }
    }
}
