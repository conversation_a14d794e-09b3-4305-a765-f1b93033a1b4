<?php

namespace App\Utils;

class CommonUtil
{
    public static function validateIDCard($idCard): bool
    {
        // 移除空格和横杠
        $idCard = trim(str_replace('-', '', $idCard));
        // 长度验证
        $length = strlen($idCard);
        if ($length !== 15 && $length !== 18) {
            return false;
        }

        // 15位转18位
        if ($length === 15) {
            $idCard = self::convert15To18($idCard);
        }
        // 格式验证
        $pattern = '/^[1-9]\d{5}(18|19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}[0-9Xx]$/';
        if (!preg_match($pattern, $idCard)) {
            return false;
        }
        // 日期验证
        $year = (int)substr($idCard, 6, 4);
        $month = (int)substr($idCard, 10, 2);
        $day = (int)substr($idCard, 12, 2);
        if (!checkdate($month, $day, $year)) {
            return false;
        }
        // 校验码验证
        return self::validateChecksum($idCard);
    }

    // 15位转18位
    private static function convert15To18($idCard15): string
    {
        // 补全年份（19xx）
        $idCard17 = substr($idCard15, 0, 6) . '19' . substr($idCard15, 6);

        // 计算校验码
        $factors = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];
        $checkCodes = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'];

        $sum = 0;
        for ($i = 0; $i < 17; $i++) {
            $sum += (int)$idCard17[$i] * $factors[$i];
        }

        $mod = $sum % 11;
        return $idCard17 . $checkCodes[$mod];
    }

    // 校验码验证
    private static function validateChecksum($idCard18): bool
    {
        $factors = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];
        $checkCodes = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'];
        $sum = 0;
        for ($i = 0; $i < 17; $i++) {
            $sum += (int)$idCard18[$i] * $factors[$i];
        }
        $mod = $sum % 11;
        $expectedCode = $checkCodes[$mod];
        $actualCode = strtoupper($idCard18[17]);
        return $actualCode === $expectedCode;
    }
}
