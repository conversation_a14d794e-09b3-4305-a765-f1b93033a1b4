<?php

namespace App\Models;

use App\Traits\Model\UpdateBatch;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * @property MatchCenterGroup $group
 * @property string $level
 * @property int $id
 */
class MatchCenterGroupLevel extends Model
{
    use UpdateBatch;

    //
    public $guarded = [];
    public $casts = [
        'sex' => 'integer',
    ];

    public function group(): BelongsTo
    {
        return $this->belongsTo(MatchCenterGroup::class, 'match_center_group_id', 'id');
    }

    public function users(): HasMany
    {
        return $this->hasMany(User::class, 'match_center_level_id');
    }

    public function details(): HasMany
    {
        return $this->hasMany(MatchDetail::class, 'match_center_level_id');
    }

    public function schedules(): HasMany
    {
        return $this->hasMany(MatchCenterLevelSchedule::class, 'match_center_level_id');
    }
}
