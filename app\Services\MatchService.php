<?php

namespace App\Services;


use App\Exceptions\DataException;
use App\Models\MatchCenter;
use App\Models\MatchCenterGroupLevel;
use App\Models\MatchDetail;
use App\Models\User;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class MatchService
{

    protected array $details = [];

    protected function getDetails($matchCenterLevelId)
    {
        if (!Arr::has($this->details, $matchCenterLevelId)) {
            $this->details[$matchCenterLevelId] = MatchDetail::query()
                ->with([
                    'user1',
                    'user2',
                    'winnerUser',
                ])
                ->where('match_center_level_id', $matchCenterLevelId)
                ->get();
        }
        return $this->details[$matchCenterLevelId];
    }

    /**
     * 获取比赛选手信息
     * @param MatchDetail $detail 比赛详情
     * @param int $type 1表示红角色(user1), 2表示蓝角色(user2)
     * @return array 返回选手信息数组
     */
    public function getCenterUser(MatchDetail $detail, $type)
    {
        // 预加载所有相关数据以提高性能
        $details =clone $this->getDetails($detail->match_center_level_id);
        // 1 红角色
        if ($type == 1) {
            if ($detail->user1_id) {
                $cacheDetail = $details->where('id', $detail->id)->first();
                return "{$cacheDetail->user1->name}({$cacheDetail->user1->team_name})";
            }
            if ($detail->parent_match1_id) {
                $cacheDetail = $details->where('id', $detail->parent_match1_id)->first();
                return $this->getCenterUser($cacheDetail, 3);
            }
            return "轮空";
        }
        if ($type == 2) {
            if ($detail->user2_id){
                $cacheDetail = $details->where('id', $detail->id)->first();
                return "{$cacheDetail->user2->name}({$cacheDetail->user2->team_name})";
            }
            if ($detail->parent_match2_id){
                $cacheDetail = $details->where('id', $detail->parent_match2_id)->first();
                return $this->getCenterUser($cacheDetail, 3);
            }
            return "轮空";
        }
        if ($type == 3) {
            // 获取 winner
            if ($detail->winnerUser){
                return "{$detail->winnerUser->name}({$detail->winnerUser->team_name})";
            }
            return "未决出";
        }
        return "轮空";
    }

    /**
     * 获取比赛轮次和轮空次数以及第一轮比赛场次
     * @param $numPlayers
     * @return int[]
     */
    public function calculateTournamentFullInfo($numPlayers): array
    {
        // 处理特殊情况：0-1人无需比赛
        if ($numPlayers <= 1) {
            return [
                'rounds' => 0,
                'round_byes' => 0,
                'round_matches' => 0
            ];
        }

        // 计算总轮数：找到大于等于参赛人数的最小2的幂，其指数即为轮数
        $rounds = 0;
        $nextPower = 1;
        while ($nextPower < $numPlayers) {
            $nextPower *= 2;
            $rounds++;
        }

        // 计算第一轮轮空人数和比赛场数
        // 轮空人数 = 下一个2的幂 - 参赛人数（如果参赛人数不是2的幂）
        // 当参赛人数是2的幂时，轮空人数为0
        if (($numPlayers & ($numPlayers - 1)) == 0) {
            // 是2的幂次
            $firstRoundByes = 0;
            $firstRoundMatches = $numPlayers / 2;
        } else {
            // 不是2的幂次
            $firstRoundByes = $nextPower - $numPlayers;
            $firstRoundMatches = ($numPlayers - $firstRoundByes) / 2;
        }

        return [
            'rounds' => $rounds,
            'round_byes' => $firstRoundByes,
            'round_matches' => (int)$firstRoundMatches
        ];
    }

    public function deepDraw(MatchCenterGroupLevel $matchCenterLevel, $details = null): void
    {
        // 先获取全部
        if (!$details) {
            $details = $matchCenterLevel->details()->where('step', 1)->get();
        }
        $details = collect($details);
        // 递归往打到决赛
        $details->groupBy('group')->each(function ($matchDetails) use ($matchCenterLevel) {
            $nowCount = $matchDetails->count();
            $nowRound = $matchDetails->max('round');
            $newMatchDetails = [];
            // 剩余一半比赛
            foreach (range(1, $nowCount / 2) as $item) {
                /**
                 * @var MatchDetail $matchDetail ;
                 */
                $matchDetail = $matchDetails->pop(1);
                $matchDetail2 = $matchDetails->pop(1);
                $newMatchDetail = $matchDetail->replicate(['id', 'user1_id', 'user2_id']);
                $nowRound++;
                $newMatchDetails[] = $newMatchDetail->fill([
                    'step' => $matchDetail->step + 1,
                    'round' => Str::padLeft($nowRound, 2, '0'),
                    'parent_match1_id' => $matchDetail->id,
                    'parent_match2_id' => $matchDetail2->id,
                ]);
            }
            $res = $matchCenterLevel->details()->saveMany($newMatchDetails);
            if ($newMatchDetail->step < $newMatchDetail->total_step) {
                $this->deepDraw($matchCenterLevel, $res);
            }
        });
    }

    /**
     * 生成第一轮对阵
     * @param MatchCenterGroupLevel $matchCenterLevel
     * @param int $groupCount 每组最大人数
     * @param bool $teamAvoidance 同组规避
     * @return array
     * @throws DataException
     */
    public function firstDraw(MatchCenterGroupLevel $matchCenterLevel, $groupCount = 2, $teamAvoidance = false): array
    {
        // 1v1分组
        $totalUserCount = $matchCenterLevel->users->count();
        if ($totalUserCount < 2) {
            throw new DataException('比赛报名人数不足');
        }
        $maxGroup = ceil($totalUserCount / $groupCount);
        // 已经抽过的用户
        $drawUserIds = [];
        // 分组,根据人数和一组人数,分组
        foreach (range(1, $maxGroup) as $group) {
            $round = 1;
            // 这里计算了最后一组数量是剩余人数
            if ($group * $groupCount > $totalUserCount) {
                $groupCount = $totalUserCount - (($group - 1) * $groupCount);
            }
            // 分组的信息
            $groupInfo = $this->calculateTournamentFullInfo($groupCount);
            // 本轮次比赛
            foreach (range(1, $groupInfo['round_matches']) as $item) {
                // 至少有一个选手
                $drawUser1 = $matchCenterLevel->users->whereNotIn('id', $drawUserIds)->random();
                $drawUserIds[] = $drawUser1->id;
                $drawUser2 = null;
                if ($teamAvoidance) {
                    // 尝试规避同队
                    if ($matchCenterLevel->users
                        ->whereNotIn('id', $drawUserIds)
                        ->where('team_name', '!=', $drawUser1->team_name)
                        ->count()) {
                        $drawUser2 = $matchCenterLevel->users
                            ->whereNotIn('id', $drawUserIds)
                            ->where('team_name', '!=', $drawUser1->team_name)
                            ->random();
                    }
                }
                // 规避不了了
                if (!$drawUser2 instanceof User) {
                    $drawUser2 = $matchCenterLevel->users->whereNotIn('id', $drawUserIds)->random();
                }

                $matchDetails[] = new MatchDetail([
                    'match_center_level_id' => $matchCenterLevel->id,
                    'match_center_group_id' => $matchCenterLevel->group->id,
                    'match_center_project_id' => $matchCenterLevel->group->project->id,
                    'match_center_id' => $matchCenterLevel->group->project->match_center_id,
                    'group' => $group,
                    'total_step' => $groupInfo['rounds'],
                    'step' => 1,
                    'round' => Str::padLeft($round, 2, '0'),
                    'user1_id' => $drawUser1->id,
                    'user2_id' => $drawUser2->id,
                ]);
                $drawUserIds[] = $drawUser2->id;
                $round++;
            }
            // 轮空比赛
            foreach (range(1, $groupInfo['round_byes']) as $item) {
                $drawUser = $matchCenterLevel->users->whereNotIn('id', $drawUserIds)->random();
                $drawUserIds[] = $drawUser->id;
                $matchDetails[] = new MatchDetail([
                    'match_center_level_id' => $matchCenterLevel->id,
                    'match_center_group_id' => $matchCenterLevel->group->id,
                    'match_center_project_id' => $matchCenterLevel->group->project->id,
                    'match_center_id' => $matchCenterLevel->group->project->match_center_id,
                    'group' => $group,
                    'round' => Str::padLeft($round, 2, '0'),
                    'total_step' => $groupInfo['rounds'],
                    'step' => 1,
                    'user1_id' => $drawUser->id,
                    'user2_id' => null,
                ]);
                $round++;
            }
        }
        $matchCenterLevel->details()->delete();
        $matchCenterLevel->update(['each_group_count' => $groupCount, 'status' => 1]);
        return $matchCenterLevel->details()->saveMany($matchDetails);
    }
}
