<?php

namespace App\Api\Manage;

use Guz<PERSON><PERSON>ttp\Client as GuzzleClient;
use GuzzleHttp\Middleware;
use Illuminate\Http\Client\PendingRequest;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Psr\Http\Message\RequestInterface;
use Psr\Http\Message\ResponseInterface;

class ManageClient extends PendingRequest
{

    protected $baseUrl = "";

    public function __construct(array $config = [])
    {
        parent::__construct();
        $config = config('services.saishiguanli');
        $this->baseUrl = Arr::get($config, 'domain');
        $this->withMiddleware(Middleware::mapResponse(function (ResponseInterface $response) {
            if (!in_array($response->getStatusCode(), [200, 201, 202])) {
                Log::error('赛事系统请求报错:' . $response->getBody()->getContents());
            }
            return $response;
        }));
    }


    public function createMatchCenter($param)
    {
        $path = '/api/openapi/match-centers';
        $response = $this->asJson()->post($path, $param);
        return $response->json();
    }

    public function updateMatchCenter($id, $param)
    {
        $path = "/api/openapi/match-centers/{$id}";
        $response = $this->asJson()->put($path, $param);
        return $response->json();
    }

    public function updateMatchCenterProjectOptions($id, $param)
    {
        $path = "/api/openapi/match-centers/{$id}";
        $response = $this->asJson()->post($path, $param);
        return $response->json();
    }

    public function getMatchCenterRegistrations($id)
    {
        $path = "/api/openapi/match-centers/{$id}/registrations";
        $response = $this->asJson()->get($path);
        return $response->json();
    }

    public function syncMatchCenterRegistrations($id, $data)
    {
        $path = "/api/openapi/match-centers/{$id}/registrations/sync";
        $response = $this->asJson()->post($path,$data);
        return $response->json();
    }

}
