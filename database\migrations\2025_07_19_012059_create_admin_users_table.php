<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('admin_users', function (Blueprint $table) {
            $table->id();
            $table->string('account')->comment('账号');
            $table->string('name')->comment('姓名');
            $table->string('password')->nullable()->comment('密码');
            $table->boolean('enable')->default(true)->comment('启用');
            $table->timestamps();
            $table->comment('后台用户表');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('admin_users');
    }
};
