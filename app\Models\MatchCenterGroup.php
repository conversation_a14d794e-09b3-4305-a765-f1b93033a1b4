<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @property Carbon $min_birthday_date
 * @property Carbon $max_birthday_date
 * @property string $name
 * @property MatchCenterProject $project
 * @property int $id
 */
class MatchCenterGroup extends Model
{
    use SoftDeletes;

    //
    public $guarded = [];
    public $casts = [
        'min_birthday_date' => 'date',
        'max_birthday_date' => 'date',
    ];

    public function project(): BelongsTo
    {
        return $this->belongsTo(MatchCenterProject::class, 'match_center_project_id', 'id');
    }

    public function levels(): HasMany
    {
        return $this->hasMany(MatchCenterGroupLevel::class, 'match_center_group_id', 'id');
    }
}
