<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Role extends \Spatie\Permission\Models\Role
{
    use HasFactory;

    //
    public const ADMIN = 'admin';
    public const OPERATION = 'operation';
    public const AUDITOR = 'auditor';
    public const CUSTOMER_SERVICE = 'customer_service';

    public const ROLE_LIST = [
        self::ADMIN => '超级管理员',
        self::OPERATION => '运营人员',
        self::AUDITOR => '审核专员',
        self::CUSTOMER_SERVICE => '客服专员',
    ];

    protected function serializeDate(\DateTimeInterface $date)
    {
        return $date->format($this->dateFormat ?: 'Y-m-d H:i:s');
    }
}
