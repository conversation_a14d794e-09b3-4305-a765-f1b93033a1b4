<?php

use App\Http\Controllers\MiniProgram\AuthController;
use App\Http\Controllers\MiniProgram\UserController;
use Illuminate\Support\Facades\Route;


## 小程序非登录
Route::group([], function () {
    ## 登录AUTH
    Route::group([], function () {
        // todo:: 需要实现登录验证密码部分
        Route::post('auth/login', [AuthController::class, 'login']);
        // todo:: 需要实现微信登录 参考内衣 google登录等
        Route::post('auth/wechat-login', [AuthController::class, 'loginWechat']);
    });
});


## 小程序登录
Route::middleware(['auth:sanctum', 'user.check'])->group(function () {
    ## 认证信息
    Route::group([], function () {
        Route::get('user/info', [UserController::class, 'me']);
    });
});
