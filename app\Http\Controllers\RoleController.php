<?php

namespace App\Http\Controllers;

use App\Constants\Permissions;
use App\Http\Controllers\Controller;
use App\Models\AdminUser;
use App\Models\Permission;
use App\Models\Role;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rules\Enum;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\QueryBuilder;
use Illuminate\Validation\Rule;

class RoleController extends Controller
{
    public function index(Request $request): AnonymousResourceCollection
    {
        $query = QueryBuilder::for(Role::class)
            ->with([
                'permissions',
            ])
            ->allowedFilters([
                AllowedFilter::partial('name'),
            ])
            ->defaultSort('-id');
        $list = $query->paginate($this->getPerPage());
        return JsonResource::collection($list);
    }

    public function show(Role $role): JsonResource
    {
        $role->loadMissing(['permissions']);
        return JsonResource::make($role);
    }

    public function update(Request $request, Role $role): JsonResource
    {
        $validated = $request->validate([
            'name' => [
                'required',
                'max:100',
                Rule::unique('roles')->ignore(optional($role)->id)
            ],
            'permissions' => 'array',
            'permissions.*' => [new Enum(Permissions::class)],
            'desc' => 'nullable|max:255',
        ]);
        $permissions = Arr::pull($validated, 'permissions', []);
        /**
         * @var AdminUser $admin
         */
        $admin = Auth::user();
        // 修改基础
        $role->update($validated);
        // 权限不应该超越本人
        if (!is_null($permissions)) {
            $allPermissions = $admin->getAllPermissions()->pluck('name')->toArray();
            $permissions = array_values(array_intersect($allPermissions, $permissions));
            $role->syncPermissions($permissions);
        }

        $role->loadMissing(['permissions']);
        return JsonResource::make($role);
    }

    /**
     * 创建用户
     * @param Request $request
     * @param Role $role
     * @return JsonResource
     */
    public function store(Request $request, Role $role): JsonResource
    {
        $admin = Auth::user();
        $allPermissions = $admin->getAllPermissions()->pluck('name')->toArray();
        $validated = $request->validate([
            'name' => [
                'required',
                'max:100',
                Rule::unique('roles')->ignore(optional($role)->id)
            ],
            'desc' => 'required|max:255',
            'permissions' => 'array',
            'permissions.*' => [new Enum(Permissions::class, 'name')],
        ]);
        // 本阶段可以无条件修改
        $permissions = Arr::pull($validated, 'permissions', []);
        $role->fill([
            ...$validated,
            'guard_name' => 'admin',
        ])->save();
        $admin = Auth::user();
        // 权限不应该超越本人
        if (!is_null($permissions)) {
            $allPermissions = $admin->getAllPermissions()->pluck('name')->toArray();
            $permissions = array_values(array_intersect($allPermissions, $permissions));
            $role->syncPermissions($permissions);
        }
        $role->loadMissing(['permissions']);
        return JsonResource::make($role);
    }


    /**
     * 批量删除角色
     * @param Request $request
     * @return JsonResource
     */
    public function batchDelete(Request $request): JsonResource
    {
        $ids = $request->validate([
            'ids' => 'required|array',
            'ids.*' => 'exists:roles,id',
        ], ['ids.*.exists' => "不存在的角色id::input"])['ids'];

        // 使用whereIn查询确保获取到的都是有效的Role对象
        $roles = Role::query()->whereIn('id', $ids)->get();

        foreach ($roles as $role) {
            // 先解除所有权限关联
            $role->permissions()->detach();
            // 再删除角色
            $role->delete();
        }

        return JsonResource::make([]);
    }


    public function permissions()
    {
        return JsonResource::collection(Permission::get());
    }

}
