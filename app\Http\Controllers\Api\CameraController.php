<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Camera;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\JsonResource;
use Carbon\Carbon;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\QueryBuilder;
use Exception;

class CameraController extends Controller
{
    /**
     * 摄像机上报接口
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function report(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'venue_number' => 'required|string|max:50',
                'camera_mac' => 'required|string|max:100',
                'remarks' => 'nullable|string'
            ]);

            $venueNumber = $validated['venue_number'];
            $cameraMac = $validated['camera_mac'];
            $remarks = $validated['remarks'];
            $now = Carbon::now();

            // 使用 Spatie QueryBuilder 查找现有记录
            $existingCamera = QueryBuilder::for(Camera::class)
                ->where('camera_mac', $cameraMac)
                ->first();

            $isNew = false;
            $camera = null;

            if ($existingCamera) {
                // 更新现有记录
                $existingCamera->update([
                    'venue_number' => $venueNumber,
                    'remarks' => $remarks,
                    'last_report_time' => $now,
                ]);
                $camera = $existingCamera->fresh();
            } else {
                // 创建新记录
                $camera = Camera::create([
                    'venue_number' => $venueNumber,
                    'camera_mac' => $cameraMac,
                    'remarks' => $remarks,
                    'last_report_time' => $now,
                ]);
                $isNew = true;
            }

            return response()->json([
                'success' => true,
                'message' => $isNew ? '摄像机注册成功' : '摄像机上报时间更新成功',
                'data' => [
                    'id' => $camera->id,
                    'venue_number' => $camera->venue_number,
                    'camera_mac' => $camera->camera_mac,
                    'remarks' => $camera->remarks,
                    'last_report_time' => $camera->last_report_time ? $camera->last_report_time->format('Y-m-d H:i:s') : null,
                    'is_new' => $isNew,
                ],
            ], 200);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '服务器内部错误',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * 获取摄像机列表
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        try {
            // 使用 Spatie QueryBuilder 处理查询和筛选
            $cameras = QueryBuilder::for(Camera::class)
                ->allowedFilters([
                    AllowedFilter::partial('venue_number'),     // 支持场地号模糊查找
                    AllowedFilter::partial('camera_mac'),       // 支持摄像机MAC模糊查找
                ])
                ->allowedSorts([
                    'venue_number',
                    'camera_mac',
                    'last_report_time',
                    'created_at',
                    'updated_at'
                ])
                ->defaultSort('-last_report_time')  // 默认按最后上报时间降序
                ->paginate($request->input('per_page', 15));

            // 格式化数据
            $currentTime = Carbon::now();
            $data = $cameras->getCollection()->map(function ($camera) use ($currentTime) {
                // 计算在线状态：最后上报时间与当前时间差小于6秒为在线
                $isOnline = false;
                $status = 'offline';
                $statusText = '离线';

                if ($camera->last_report_time) {
                    $lastReportTime = Carbon::parse($camera->last_report_time);
                    $secondsDiff = $currentTime->diffInSeconds($lastReportTime);

                    if ($secondsDiff <= 6) {
                        $isOnline = true;
                        $status = 'online';
                        $statusText = '在线';
                    }
                }

                return [
                    'id' => $camera->id,
                    'venue_number' => $camera->venue_number,
                    'camera_mac' => $camera->camera_mac,
                    'remarks' => $camera->remarks,
                    'last_report_time' => $camera->last_report_time ? $camera->last_report_time->format('Y-m-d H:i:s') : null,
                    'created_at' => $camera->created_at ? $camera->created_at->format('Y-m-d H:i:s') : null,
                    'updated_at' => $camera->updated_at ? $camera->updated_at->format('Y-m-d H:i:s') : null,
                    'is_online' => $isOnline,
                    'status' => $status,
                    'status_text' => $statusText,
                ];
            });

            return response()->json([
                'success' => true,
                'message' => '获取成功',
                'data' => $data,
                'pagination' => [
                    'current_page' => $cameras->currentPage(),
                    'last_page' => $cameras->lastPage(),
                    'per_page' => $cameras->perPage(),
                    'total' => $cameras->total(),
                ],
            ], 200);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '服务器内部错误',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * 编辑摄像机信息
     * 
     * @param Request $request
     * @param Camera $camera
     * @return JsonResource
     */
    public function update(Request $request, Camera $camera): JsonResource
    {
        // 验证请求参数
        $validated = $request->validate([
            'venue_number' => 'required|string|max:50',
            'camera_mac' => 'required|string|max:100',
            'remarks' => 'nullable|string',
        ]);

        $camera->update($validated);

        return JsonResource::make($camera);
    }
}
