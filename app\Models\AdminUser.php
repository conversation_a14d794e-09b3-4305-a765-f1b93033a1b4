<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Casts\Attribute;
use <PERSON><PERSON>\Sanctum\HasApiTokens;
use Spatie\Permission\Traits\HasPermissions;
use Spatie\Permission\Traits\HasRoles;
use Illuminate\Foundation\Auth\User as Authenticatable;

/**
 * @property int $id
 */
class AdminUser extends Authenticatable
{
    use HasApiTokens;
    use HasPermissions;
    use HasRoles;

    // 角色常量
    const ROLE_SUPER_ADMIN = 'super-admin';
    const ROLE_ADMIN = 'admin';
    const ROLE_MANAGER = 'manager';
    const ROLE_OPERATOR = 'operator';

    // 状态常量
    const STATUS_ENABLED = 1;
    const STATUS_DISABLED = 0;

    protected $guarded = [];
    protected $casts = [
        'last_login_date' => 'datetime',
        'enable' => 'boolean',
    ];

    protected $hidden = ['password'];

    // protected function serializeDate(DateTimeInterface $date)
    protected function serializeDate(\DateTimeInterface $date)
    {
        return $date->format($this->dateFormat ?: 'Y-m-d H:i:s');
    }

    // 重要， 用于permission
    public function guardName(): string
    {
        return 'admin';
    }

    /**
     * 密码
     * @return Attribute
     */
    public function password(): Attribute
    {
        return Attribute::set(fn($v) => bcrypt($v));
    }

    /**
     * 按角色名称搜索用户
     */
    public function scopeRoleName($query, $roleName)
    {
        return $query->whereHas('roles', function ($roleQuery) use ($roleName) {
            $roleQuery->where('name', 'like', '%' . $roleName . '%');
        });
    }
}
