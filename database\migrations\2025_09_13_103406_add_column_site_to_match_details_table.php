<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('match_details', function (Blueprint $table) {
            //
            $table->string('site')->nullable()->after('referee_t_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('match_details', function (Blueprint $table) {
            //
            $table->dropColumn([
                'site'
            ]);
        });
    }
};
