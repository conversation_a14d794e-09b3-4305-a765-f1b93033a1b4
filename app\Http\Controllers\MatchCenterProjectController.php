<?php

namespace App\Http\Controllers;

use App\Exceptions\DataException;
use App\Http\Resources\MatchCenterGroupLevelResource;
use App\Models\AdminUser;
use App\Models\Enums\MatchCenter\ProjectEnterType;
use App\Models\Enums\MatchCenter\ProjectType;
use App\Models\Enums\MatchCenter\ProjectSexType;
use App\Models\Enums\MatchCenter\ProjectSourceType;
use App\Models\Enums\MatchCenterLevel;
use App\Models\Enums\MatchCenterStatus;
use App\Models\MatchCenter;
use App\Models\MatchCenterGroup;
use App\Models\MatchCenterGroupLevel;
use App\Models\MatchCenterLevelSchedule;
use App\Models\MatchCenterProject;
use App\Models\MatchDetail;
use App\Models\Referee;
use App\Models\User;
use App\Services\MatchService;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Illuminate\Validation\Rules\Enum;
use Illuminate\Validation\Rules\Exists;
use Illuminate\Validation\ValidationException;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\QueryBuilder;

class MatchCenterProjectController extends Controller
{
    //

    public function options(Request $request, MatchCenter $matchCenter): AnonymousResourceCollection
    {
        $matchCenter->loadMissing([
            'projects:id,id as projectId,match_center_id,project,project as projectName',
            'projects.groups:id,id as groupId,name,name as groupName,match_center_project_id,max_birthday_date,min_birthday_date',
            'projects.groups.levels:id,id as levelId,level as levelName,match_center_group_id,level,sex',
        ]);
        $projects = $matchCenter->projects->toArray();
        return JsonResource::collection($projects);
    }
    /**
     * 项目列表
     * @param Request $request
     * @return AnonymousResourceCollection
     */
    public function index(Request $request, MatchCenter $matchCenter): AnonymousResourceCollection
    {
        $builder = QueryBuilder::for(MatchCenterProject::class, $request)
            ->where('match_center_id', $matchCenter->id)
            ->allowedFilters([
                AllowedFilter::partial('project'),
                AllowedFilter::exact('project_type'),
                AllowedFilter::exact('sex_type'),
                AllowedFilter::exact('source_type'),
                AllowedFilter::exact('enter_type'),
            ])
            ->defaultSort('-id');
        return JsonResource::collection($builder->paginate($this->getPerPage()));
    }

    /**
     * 创建项目
     * @param Request $request
     * @param MatchCenter $matchCenter
     * @return JsonResource
     */
    public function store(Request $request, MatchCenter $matchCenter, MatchCenterProject $project): JsonResource
    {
        $validated = $request->validate([
            'project' => 'required|string|max:255',
            'project_type' => ['required', new Enum(ProjectType::class)],
            'source_type' => ['required', new Enum(ProjectSourceType::class)],
            'sex_type' => ['required', new Enum(ProjectSexType::class)],
            'enter_type' => ['required', new Enum(ProjectEnterType::class)],
            'service_fee' => ['required', 'numeric'],
        ]);
        $project->fill($validated);
        $matchCenter->projects()->save($project);
        return JsonResource::make($project);
    }

    /**
     * 修改
     * @param Request $request
     * @param MatchCenter $matchCenter
     * @return JsonResource
     */
    public function update(Request $request, MatchCenter $matchCenter, MatchCenterProject $project): JsonResource
    {
        $validated = $request->validate([
            'project' => 'required|string|max:255',
            'project_type' => ['required', new Enum(ProjectType::class)],
            'source_type' => ['required', new Enum(ProjectSourceType::class)],
            'sex_type' => ['required', new Enum(ProjectSexType::class)],
            'enter_type' => ['required', new Enum(ProjectEnterType::class)],
            'service_fee' => ['required', 'numeric'],
        ]);
        $project->update($validated);
        return JsonResource::make($project);
    }

    /**
     * 删除
     * @param Request $request
     * @param MatchCenter $matchCenter
     * @param MatchCenterProject $project
     * @return JsonResource
     */
    public function destroy(Request $request, MatchCenter $matchCenter, MatchCenterProject $project): JsonResource
    {
        $project->delete();
        return JsonResource::make([]);
    }


    /**
     * 组别列表
     * @param Request $request
     * @return AnonymousResourceCollection
     */
    public function groupIndex(Request $request, MatchCenterProject $project): AnonymousResourceCollection
    {
        $builder = QueryBuilder::for(MatchCenterGroup::class, $request)
            ->where('match_center_project_id', $project->id)
            ->with(['levels'])
            ->allowedFilters([
                AllowedFilter::partial('name'),
            ])
            ->defaultSort('-id');
        return JsonResource::collection($builder->paginate($this->getPerPage()));
    }

    /**
     * 创建组别
     * @param Request $request
     * @param MatchCenterProject $project
     * @param MatchCenterGroup $group
     * @return JsonResource
     */
    public function groupStore(Request $request, MatchCenterProject $project, MatchCenterGroup $group): JsonResource
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'max_birthday_date' => ['required', 'date'],
            'min_birthday_date' => ['required', 'date'],
        ]);
        $group->fill($validated);
        $project->groups()->save($group);
        return JsonResource::make($group);
    }

    /**
     * 组别删除
     * @param Request $request
     * @param MatchCenterGroup $group
     * @param MatchCenterProject $project
     * @return JsonResource
     */
    public function groupDestroy(Request $request, MatchCenterProject $project, MatchCenterGroup $group): JsonResource
    {
        $group->delete();
        // todo:: 报名阶段可更新
        return JsonResource::make([]);
    }

    public function groupFastGrading(Request $request, MatchCenterProject $project, MatchCenterGroup $group): JsonResource
    {
        $validated = $request->validate([
            'min_value' => 'required|numeric',
            'step' => 'required|numeric',
            'count' => 'required|numeric|min:1',
            'level_template' => 'required|string',
        ]);
        $levelTemplate = $validated['level_template'];
        if (!Str::contains($levelTemplate, ['%s'])) {
            throw ValidationException::withMessages([
                'level_template' => ['模板格式错误!'],
            ]);
        }
        $startValue = $validated['min_value'];
        $step = $validated['step'];
        $levels = [];
        foreach (range(0, $validated['count'] - 1) as $value) {
            $levels[] = new MatchCenterGroupLevel([
                'sex' => 1,
                'level' => sprintf($levelTemplate, $startValue + $value * $step),
            ]);
            $levels[] = new MatchCenterGroupLevel([
                'sex' => 2,
                'level' => sprintf($levelTemplate, $startValue + $value * $step),
            ]);
        }
        // 删除之前的, 保存当前的
        $group->levels()->delete();
        $group->levels()->saveMany($levels);

        return JsonResource::make([]);
    }

    /**
     * 组别修改
     * @param Request $request
     * @param MatchCenterProject $project
     * @param MatchCenterGroup $group
     * @return JsonResource
     */
    public function groupUpdate(Request $request, MatchCenterProject $project, MatchCenterGroup $group): JsonResource
    {
        $validated = $request->validate([
            'name' => 'string|max:255',
            'max_birthday_date' => ['date'],
            'min_birthday_date' => ['date'],
            'levels' => ['array'],
            'levels.*.id' => ['nullable', 'int', (new Exists(MatchCenterGroupLevel::class))->where('match_center_group_id', $group->id)],
            'levels.*.sex' => ['required'],
            'levels.*.level' => ['required', 'string'],
        ]);
        $levels = Arr::pull($validated, 'levels');
        if (is_array($levels)) {
            $addLevels = [];
            $updateLevels = [];
            foreach ($levels as $level) {
                if (Arr::has($level, 'id')) {
                    $updateLevels[] = $level;
                } else {
                    $addLevels[] = new MatchCenterGroupLevel($level);
                }
            }
            $updateIds = array_filter(Arr::pluck($updateLevels, 'id'));
            // 删掉旧的
            $res = $group->levels()->whereNotIn('id', Arr::wrap($updateIds))->delete();
            if ($addLevels) {
                $group->levels()->saveMany($addLevels);
            }
            if ($updateLevels) {
                MatchCenterGroupLevel::updateBatch($updateLevels);
            }
        }
        // base数据更新
        if ($validated) {
            $group->update($validated);
        }
        return JsonResource::make($group);
    }


    /**
     * 组别列表
     * @param Request $request
     * @return AnonymousResourceCollection
     */
    public function levelIndex(Request $request, MatchCenter $matchCenter): AnonymousResourceCollection
    {
        $builder = QueryBuilder::for(MatchCenterGroupLevel::class, $request)
            ->whereHas('group.project', function ($query) use ($matchCenter) {
                $query->where('match_center_id', $matchCenter->id);
            })
            ->with([
                'group',
                'group.project',
            ])
            ->withCount(['users'])
            ->allowedFilters([
                AllowedFilter::partial('project_name', 'group.project.name'),
            ])
            ->defaultSort('-id');
        return MatchCenterGroupLevelResource::collection($builder->paginate($this->getPerPage()));
    }

    /**
     * 组别列表
     * @param Request $request
     * @return AnonymousResourceCollection
     */
    public function levelScheduleIndex(Request $request, MatchCenter $matchCenter): AnonymousResourceCollection
    {
        $builder = QueryBuilder::for(MatchCenterGroupLevel::class, $request)
            ->whereHas('group.project', function ($query) use ($matchCenter) {
                $query->where('match_center_id', $matchCenter->id);
            })
            ->with([
                'group',
                'group.project',
                'schedules',
            ])
            ->withCount(['users'])
            ->allowedFilters([
                AllowedFilter::partial('project_name', 'group.project.name'),
                AllowedFilter::exact('site'),
                AllowedFilter::exact('match_at'),
                AllowedFilter::exact('match_center_level_id'),
            ])
            ->defaultSort('-id');
        return MatchCenterGroupLevelResource::collection($builder->paginate($this->getPerPage()));
    }

    public function levelScheduleStore(Request $request,MatchCenter $matchCenter, MatchCenterGroupLevel $level)
    {
        $validated = $request->validate([
            'schedules' => ['required', 'array'],
            'schedules.*.day' => ['string', 'required'],
            'schedules.*.time' => ['string', 'required'],
        ]);
        $details = $level->details()->orderBy('round')->get();
        // 绑定比赛
        $schedules = array_map(function ($item) use ($details) {
            $detail = $details->pop();
            $item['match_detail_id'] = $detail->id;
            return new MatchCenterLevelSchedule($item);
        }, $validated['schedules']);
        // 删除旧的,保存新的
        $level->schedules()->delete();
        $level->schedules()->saveMany($schedules);

        return MatchCenterGroupLevelResource::make($level);
    }


    /**
     * 调整比赛
     * @param Request $request
     * @param MatchCenter $matchCenter
     * @param MatchCenterGroupLevel $level
     * @return JsonResource
     */
    public function levelDetailsUpdate(Request $request, MatchCenter $matchCenter, MatchCenterGroupLevel $level): JsonResource
    {
        $validated = $request->validate([
            'details' => ['required', 'array'],
            'details.*.id' => ['required', (new Exists(MatchDetail::class, 'id'))->where('match_center_level_id', $level->id)],
            'details.*.user1_id' => ['nullable', (new Exists(User::class, 'id'))->where('match_center_level_id', $level->id)],
            'details.*.user2_id' => ['nullable', (new Exists(User::class, 'id'))->where('match_center_level_id', $level->id)],
            'details.*.referee_t_id' => ['required', (new Exists(Referee::class, 'id'))->where('match_center_id', $matchCenter->id)],
            'details.*.referee1_id' => ['required', (new Exists(Referee::class, 'id'))->where('match_center_id', $matchCenter->id)],
            'details.*.referee2_id' => ['required', (new Exists(Referee::class, 'id'))->where('match_center_id', $matchCenter->id)],
            'details.*.referee3_id' => ['required', (new Exists(Referee::class, 'id'))->where('match_center_id', $matchCenter->id)],
            'details.*.referee4_id' => ['required', (new Exists(Referee::class, 'id'))->where('match_center_id', $matchCenter->id)],
        ]);
        foreach ($validated['details'] as $update) {
            $id = Arr::pull($update, 'id');
            $detail = MatchDetail::query()->find($id);
            $detail->update($update);
        }
        return JsonResource::make([]);
    }


    public function allLevelDetails(Request $request, MatchCenter $matchCenter, MatchCenterGroupLevel $level): JsonResource
    {
        $builder = QueryBuilder::for(MatchDetail::query())
            ->with([
                'matchCenterGroupLevel:id,level',
                'matchCenterGroup:id,name',
                'matchCenterProject:id,project',
                'user1:id,name,team_name',
                'user2:id,name,team_name',
                'referee1:id,name',
                'referee2:id,name',
                'referee3:id,name',
                'referee4:id,name',
                'refereeT:id,name',
            ])
            ->where('step', 1)
            ->allowedFilters([
                AllowedFilter::exact('match_center_project_id'),
                AllowedFilter::partial('project', 'matchCenterProject.project'),
                AllowedFilter::callback('user_name', function (Builder $query, $v) {
                    $query->where(function ($q) use ($v) {
                        $likeFilter = fn($q2) => $q2->whereLike('name', "%{$v}%");
                        $q->whereHas('user1', $likeFilter)
                            ->orWhereHas('user2', $likeFilter);
                    });

                }),
                AllowedFilter::callback('referee_name', function (Builder $query, $v) {
                    $query->where(function ($q) use ($v) {
                        $likeFilter = fn($q2) => $q2->whereLike('name', "%{$v}%");
                        $q->whereHas('referee1', $likeFilter)
                            ->orWhereHas('referee2', $likeFilter)
                            ->orWhereHas('referee3', $likeFilter)
                            ->orWhereHas('referee4', $likeFilter)
                            ->orWhereHas('refereeT', $likeFilter);
                    });

                }),
            ]);
        $list = $builder->paginate($this->getPerPage());
        $list->through(function ($detail) {
            return [
                'id' => $detail->id,
                'level_id' => $detail->matchCenterGroupLevel->id,
                'level' => $detail->matchCenterGroupLevel->level,
                'group' => $detail->matchCenterGroup->name,
                'project' => $detail->matchCenterProject->project,
                'round' => $detail->round,
                'step' => $detail->step,
                'total_step' => $detail->total_step,
                'user1_id' => $detail->user1?->id ?? "轮空",
                'user1_name' => $detail->user1?->name ?? "轮空",
                'user1_team' => $detail->user1?->team_name ?? "轮空",
                'user2_id' => $detail->user2?->id ?? "轮空",
                'user2_name' => $detail->user2?->name ?? "轮空",
                'user2_team' => $detail->user2?->team_name ?? "轮空",
                'referee_t' => $detail->refereeT?->name,
                'referee1' => $detail->referee1?->name,
                'referee2' => $detail->referee2?->name,
                'referee3' => $detail->referee3?->name,
                'referee4' => $detail->referee4?->name,
                'referee_t_id' => $detail->refereeT?->id,
                'referee1_id' => $detail->referee1?->id,
                'referee2_id' => $detail->referee2?->id,
                'referee3_id' => $detail->referee3?->id,
                'referee4_id' => $detail->referee4?->id,
            ];
        });

        return JsonResource::collection($list);
    }

    /**
     * @param Request $request
     * @param MatchCenter $matchCenter
     * @param MatchCenterGroupLevel $level
     * @return JsonResource
     */
    public function levelDetails(Request $request, MatchCenter $matchCenter, MatchCenterGroupLevel $level): JsonResource
    {
        $level->loadMissing([
            'details' => function ($query) {
                $query->where('step', 1);
            },
            'details.user1:id,name,team_name',
            'details.user2:id,name,team_name',
            'details.referee1:id,name',
            'details.referee2:id,name',
            'details.referee3:id,name',
            'details.referee4:id,name',
            'details.refereeT:id,name',
        ]);
        $details = $level->details->groupBy('group')->map(function ($group) {
            return $group->map(function (MatchDetail $detail) {
                return [
                    'id' => $detail->id,
                    'round' => $detail->round,
                    'step' => $detail->step,
                    'total_step' => $detail->total_step,
                    'user1_id' => $detail->user1?->id ?? "轮空",
                    'user1_name' => $detail->user1?->name ?? "轮空",
                    'user1_team' => $detail->user1?->team_name ?? "轮空",
                    'user2_id' => $detail->user2?->id ?? "轮空",
                    'user2_name' => $detail->user2?->name ?? "轮空",
                    'user2_team' => $detail->user2?->team_name ?? "轮空",
                    'referee_t' => $detail->refereeT->name,
                    'referee1' => $detail->referee1->name,
                    'referee2' => $detail->referee2->name,
                    'referee3' => $detail->referee3->name,
                    'referee4' => $detail->referee4->name,
                    'referee_t_id' => $detail->refereeT->id,
                    'referee1_id' => $detail->referee1->id,
                    'referee2_id' => $detail->referee2->id,
                    'referee3_id' => $detail->referee3->id,
                    'referee4_id' => $detail->referee4->id,
                ];
            });
        })->values();
        return JsonResource::make($details);
    }


    /**
     * 裁判抽签
     * @param Request $request
     * @param MatchCenter $matchCenter
     * @return JsonResource
     */
    public function refereeDraw(Request $request, MatchCenter $matchCenter): JsonResource
    {
        $validated = $request->validate([
            'ids' => ['required', 'array'],
            'ids.*' => [new Exists(MatchCenterGroupLevel::class, 'id')],
            'team_avoidance' => 'bool',
        ]);
        $teamAvoidance = Arr::get($validated, 'team_avoidance', false);
        $matchCenterLevels = MatchCenterGroupLevel::query()
            ->with([
                'details',
                'details.user1',
                'details.user2',
            ])
            ->whereIn('id', $validated['ids'])
            ->get();
        $errors = [];

        $referees = $matchCenter->referees()->get();
        if ($referees->count() < 5) {
            throw new DataException("至少存在5位裁判.");
        }
        /**
         * @var MatchCenterGroupLevel $matchCenterLevel
         */
        foreach ($matchCenterLevels as $matchCenterLevel) {
            try {
                if (!$matchCenterLevel->details->count()) {
                    throw new DataException("未完成选手抽签");
                }
                /**
                 * @var MatchDetail $detail
                 */
                foreach ($matchCenterLevel->details as $detail) {
                    $tempReferees = (clone $referees)->keyBy('id');
                    if ($teamAvoidance) {
                        $igonTeam = [];
                        if ($detail->user1) {
                            $igonTeam[] = $detail->user1->team_name;
                        }
                        if ($detail->user2) {
                            $igonTeam[] = $detail->user2->team_name;
                        }
                        // 筛选符合条件的裁判
                        $eligibleReferees = $tempReferees->whereNotIn('company', $igonTeam);
                        if ($eligibleReferees->isNotEmpty()) {
                            $igonReferees = [];
                            // 循环获取最多5个符合条件的裁判
                            for ($i = 0; $i < 5; $i++) {
                                // 检查是否还有符合条件的裁判
                                if ($eligibleReferees->isEmpty()) {
                                    break;
                                }
                                // 随机获取一个符合条件的裁判并弹出
                                $referee = $eligibleReferees->random();
                                // 一起删除一个
                                $eligibleReferees->pull($referee->getKey());
                                $tempReferees->pull($referee->getKey());
                                $igonReferees[] = $referee;
                            }
                            // 解构数组赋值给变量（保持原变量名）
                            list($refereeT, $referee1, $referee2, $referee3, $referee4) = array_pad($igonReferees, 5, null);
                        }
                    }
                    $refereeT ??= $tempReferees->keyBy('id')->pull($tempReferees->random()->getKey());
                    $referee1 ??= $tempReferees->keyBy('id')->pull($tempReferees->random()->getKey());
                    $referee2 ??= $tempReferees->keyBy('id')->pull($tempReferees->random()->getKey());
                    $referee3 ??= $tempReferees->keyBy('id')->pull($tempReferees->random()->getKey());
                    $referee4 ??= $tempReferees->keyBy('id')->pull($tempReferees->random()->getKey());
                    // 修改
                    $detail->update([
                        'referee_t_id' => $refereeT->id,
                        'referee1_id' => $referee1->id,
                        'referee2_id' => $referee2->id,
                        'referee3_id' => $referee3->id,
                        'referee4_id' => $referee4->id,
                    ]);
                }
            } catch (\Throwable $e) {
                $errors[] = "{$matchCenterLevel->group->name}-{$matchCenterLevel->level}:{$e->getMessage()}";
            }
        }
        return JsonResource::make(['errors' => $errors]);
    }

    /**
     * 选手抽签
     * @param Request $request
     * @param MatchCenter $matchCenter
     * @return JsonResource
     */
    public function draw(Request $request, MatchCenter $matchCenter): JsonResource
    {
        $validated = $request->validate([
            'ids' => ['required', 'array'],
            'ids.*' => [new Exists(MatchCenterGroupLevel::class, 'id')],
            'team_avoidance' => 'bool',
            'group_count' => 'int|required|min:2',
        ]);
        $matchCenterLevels = MatchCenterGroupLevel::query()
            ->with(['group', 'group.project', 'users'])
            ->whereIn('id', $validated['ids'])
            ->get();

        $errors = [];
        /**
         * @var MatchCenterGroupLevel $matchCenterLevel
         */
        foreach ($matchCenterLevels as $matchCenterLevel) {
            try {
                $res = matchService()->firstDraw($matchCenterLevel, Arr::get($validated, 'group_count'), Arr::get($validated, 'team_avoidance', false));
                if ($res) {
                    matchService()->deepDraw($matchCenterLevel, $res);
                }
            } catch (\Throwable $e) {
                $errors[] = "{$matchCenterLevel->group->name}-{$matchCenterLevel->level}:{$e->getMessage()}";
            }
        }
        return JsonResource::make(['errors' => $errors]);
    }
}
