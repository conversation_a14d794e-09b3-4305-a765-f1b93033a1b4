<?php

namespace Database\Seeders;

use App\Constants\Permissions;
use App\Models\Permission;
use Illuminate\Database\Seeder;
use Spatie\Permission\PermissionRegistrar;

class PermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // 清除权限缓存
        app(PermissionRegistrar::class)->forgetCachedPermissions();

        // 获取所有权限定义
        $permissions = Permissions::list();

        // 创建权限
        foreach ($permissions as $permissionName => $description) {
            Permission::query()->updateOrCreate(
                [
                    'name' => $permissionName,
                    'guard_name' => 'admin'
                ],
                [
                    'desc' => $description,
                    'guard_name' => 'admin'
                ]
            );
        }

        // 重新缓存权限
        app(PermissionRegistrar::class)->forgetCachedPermissions();
    }
}
