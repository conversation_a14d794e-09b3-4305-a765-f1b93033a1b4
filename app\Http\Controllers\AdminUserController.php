<?php

namespace App\Http\Controllers;

use App\Exceptions\DataException;
use App\Models\AdminUser;
use App\Models\Attachment;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\ValidationException;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Spatie\QueryBuilder\QueryBuilder;
use Spatie\QueryBuilder\AllowedFilter;
use Illuminate\Validation\Rule;
use App\Models\Role;

class AdminUserController extends Controller
{
    //
    public function info(): JsonResource
    {
        /**
         * @var AdminUser $user
         */
        $user = Auth::user();
        $user->getAllPermissions();
        return JsonResource::make($user);
    }

    public function updateName(Request $request): JsonResource
    {
        $validated = $request->validate([
            'name' => 'required|max:255|string'
        ]);
        /**
         * @var AdminUser $user
         */
        $user = Auth::user();
        $user->update($validated);

        return JsonResource::make($user);
    }

    /**
     * 修改密码
     * @param Request $request
     * @return JsonResource
     * @throws DataException
     */
    public function updatePassword(Request $request): JsonResource
    {
        $validated = $request->validate([
//            'old_password' => 'required|max:255|string',
            'password' => 'required|max:255|string'
        ]);
//        if ($validated['old_password'] == $validated['password']) {
//            throw new DataException("新密码不可以和旧密码相同!");
//        }
//        /**
//         * @var AdminUser $user
//         */
        $user = Auth::user();
//        if (!Hash::check(Arr::get($validated, 'old_password'), $user->password)) {
//            throw new DataException("旧密码校验失败!");
//        }
        $user->update(Arr::only($validated, ['password']));

        return JsonResource::make($user);
    }


    /**
     * @param Request $request
     * @return JsonResource
     */
    public function upload(Request $request): JsonResource
    {
        $data = $request->validate([
            'file' => 'required|file|max:40960000',
            'file_name' => 'nullable|string|max:255',
        ], [
            'file.max' => '文件最大支持4G'
        ]);

        // 图片
        $file = Arr::get($data, 'file');
        /**
         * @var UploadedFile $file
         */
        $md5 = md5_file($file->getRealPath());

        if ($attachment = Attachment::query()->where('sign', $md5)->first()) {
            return JsonResource::make($attachment);
        }
        $imagePath = Carbon::today()->format("Ym/") . 'files';
        $src = $file->store($imagePath);
        $attachment = new Attachment();
        $attachment->fill([
            'file_name' => Arr::get($data, 'file_name', $file->getClientOriginalName()),
            'file_type' => $file->getMimeType(),
            'file_size' => $file->getSize(),
            'sign' => $md5,
            'path' => $src,
            'user_id' => Auth::id(),
        ])->save();
        return JsonResource::make($attachment);
    }

    public function index(Request $request): AnonymousResourceCollection
    {
        $query = QueryBuilder::for(AdminUser::class)
            ->with([
                'permissions',
                'roles.permissions'
            ])
            ->allowedFilters([
                AllowedFilter::partial('account'),
                AllowedFilter::partial('name'),
                AllowedFilter::exact('enable'),
                AllowedFilter::scope('role_name'),
            ])
            ->defaultSort('-id');
        $list = $query->paginate($this->getPerPage());
        /**
         * @var AdminUser $user
         */
        foreach ($list->items() as $user) {
            $user->{'all_permissions'} = $user->getAllPermissions()->select(['id', 'name', 'desc']);
            $user->{'all_roles'} = $user->{'roles'}->select(['id', 'name', 'desc']);
            $user->makeHidden(['permissions', 'roles']);
        }

        return JsonResource::collection($list);
    }

    public function update(Request $request, AdminUser $user)
    {
        $validated = $request->validate([
            'account' => ['required', Rule::unique('admin_users')->ignore($user), 'max:100'],
            'name' => 'required|max:100',
            'enable' => 'bool',
            'password' => 'nullable',
            'roles' => 'array',
            'roles.*' => 'exists:roles,id',

        ]);
        $roleIds = Arr::pull($validated, 'roles', []);

        // 如果没有传入密码，则从验证数据中移除 password 字段
        if (empty($validated['password'])) {
            Arr::forget($validated, 'password');
        }

        // 修改基础
        $user->update($validated);

        // 修改角色
        if (!is_null($roleIds)) {
            // 获取角色名称
            $roles = Role::whereIn('id', $roleIds)->pluck('name')->toArray();
            $user->syncRoles($roles);
        }

        return JsonResource::make($user);
    }

    /**
     * 创建用户
     * @param Request $request
     * @param AdminUser $user
     * @return JsonResource
     */
    public function store(Request $request, AdminUser $user): JsonResource
    {
        $validated = $request->validate([
            'account' => 'required|unique:admin_users|max:100',
            'name' => 'required|max:100',
            'enable' => 'bool',
            'password' => 'required',
            'roles' => 'required|array',
            'roles.*' => 'exists:roles,id',
        ]);

        // 本阶段可以无条件修改
        $roleIds = Arr::pull($validated, 'roles', []);

        // 创建用户
        $user->fill($validated)->save();

        // 修改角色
        if (!is_null($roleIds)) {
            // 获取角色名称
            $roles = Role::whereIn('id', $roleIds)->pluck('name')->toArray();
            // 绑定角色
            $user->syncRoles($roles);
        }

        return JsonResource::make($user);
    }

    public function show(AdminUser $user): JsonResource
    {
        $user->loadMissing(['roles.permissions']);
        return JsonResource::make($user);
    }

    /**
     * 批量删除管理员
     * @param Request $request
     * @return JsonResource
     */
    public function batchDelete(Request $request): JsonResource
    {
        $ids = $request->validate([
            'ids' => 'required|array',
            'ids.*' => 'exists:admin_users,id',
        ], ['ids.*.exists' => "不存在的用户id::input"])['ids'];
        AdminUser::query()->find($ids)->each(function (AdminUser $user) {
            $user->delete();
        });
        return JsonResource::make([]);
    }
}
