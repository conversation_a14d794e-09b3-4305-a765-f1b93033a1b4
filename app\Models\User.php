<?php

namespace App\Models;

use App\Models\Enums\UserSex;
use App\Models\Enums\UserStatus;
use App\Models\Enums\UserType;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Notifications\Notifiable;

class User extends Model
{
    use Notifiable;
    use SoftDeletes;

    public $guarded = [];
    public $appends = [
    ];
    protected $casts = [
        'registration_fee' => 'float',
        'is_seeded_player' => 'bool',
        'payment_at' => 'datetime',
        'sex' => UserSex::class,
        'type' => UserType::class,
        'status' => UserStatus::class
    ];


    /**
     * 所属赛事
     * @return BelongsTo
     */
    public function matchCenter(): BelongsTo
    {
        return $this->belongsTo(MatchCenter::class);
    }

    /**
     * 参赛项目
     * @return BelongsTo
     */
    public function matchCenterProject(): BelongsTo
    {
        return $this->belongsTo(MatchCenterProject::class);
    }

    /**
     * 参赛组
     * @return BelongsTo
     */
    public function matchCenterGroup(): BelongsTo
    {
        return $this->belongsTo(MatchCenterGroup::class);
    }

    /**
     * 参赛级
     * @return BelongsTo
     */
    public function matchCenterLevel(): BelongsTo
    {
        return $this->belongsTo(MatchCenterGroupLevel::class);
    }
}
