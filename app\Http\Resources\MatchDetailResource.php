<?php

namespace App\Http\Resources;

use App\Models\MatchCenterGroupLevel;
use App\Models\MatchDetail;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class MatchDetailResource extends JsonResource
{

    public function toArray(Request $request): array
    {
        /**
         * @var MatchDetail $resource
         */
        $resource = $this->resource;
        return [
            'id' => $resource->id,
            'round' => $resource->round,
        ];
    }
}
