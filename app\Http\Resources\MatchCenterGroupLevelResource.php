<?php

namespace App\Http\Resources;

use App\Models\MatchCenterGroupLevel;
use App\Models\MatchDetail;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class MatchCenterGroupLevelResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        /**
         * @var MatchCenterGroupLevel $resource
         */
        $resource = $this->resource;
        $sexDesc = match ($resource->sex) {
            1 => "男子",
            2 => "女子",
        };
        return [
            'id' => $resource->id,
            'sex' => $resource->sex,
            'level' => $resource->level,
            'each_group_count' => $resource->each_group_count,
            'group_count' => ceil($resource->users_count / $resource->each_group_count),
            'users_count' => $resource->users_count,
            'group_name' => $resource->group?->name,
            'project_name' => $resource->group?->project?->project,
            'status' => $resource->status,
            'name' => "{$sexDesc}{$resource->group?->name}",
            'schedules' => $this->whenLoaded('schedules', function () use ($resource) {
                return $resource->schedules;
            }),
        ];
    }
}
