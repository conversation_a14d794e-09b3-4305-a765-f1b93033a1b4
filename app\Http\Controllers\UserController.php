<?php

namespace App\Http\Controllers;

use App\Exceptions\DataException;
use App\Imports\UserImport;
use App\Models\Enums\UserSex;
use App\Models\Enums\UserStatus;
use App\Models\Enums\UserType;
use App\Models\MatchCenter;
use App\Models\MatchCenterGroup;
use App\Models\MatchCenterGroupLevel;
use App\Models\MatchCenterProject;
use App\Models\User;
use App\Utils\CommonUtil;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Arr;
use Illuminate\Validation\Rules\Enum;
use Illuminate\Validation\Rules\Exists;
use Illuminate\Validation\ValidationException;
use Maatwebsite\Excel\Facades\Excel;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\QueryBuilder;

class UserController extends Controller
{

    /**
     * 用户列表
     * @param Request $request
     * @param MatchCenter $matchCenter
     * @return AnonymousResourceCollection
     */
    public function index(Request $request, MatchCenter $matchCenter): AnonymousResourceCollection
    {
        $builder = QueryBuilder::for(User::class, $request)
            ->where('match_center_id', $matchCenter->id)
            ->with([
                'matchCenterProject:id,project',
                'matchCenterGroup:id,name',
                'matchCenterLevel:id,level',
            ])
            ->allowedFilters([
                AllowedFilter::exact('type')->default(UserType::Athlete),
                AllowedFilter::exact('status')->default(UserStatus::Unpublished),
                AllowedFilter::exact('is_seeded_player'),
                AllowedFilter::partial('name'),
                AllowedFilter::partial('team_name'),
                AllowedFilter::partial('id_card'),
                AllowedFilter::exact('sex'),
                AllowedFilter::exact('match_center_project_id'),
                AllowedFilter::exact('match_center_group_id'),
                AllowedFilter::exact('match_center_level_id'),
                AllowedFilter::callback('is_payment', function (Builder $q, $value) {
                    if ($value) {
                        $q->whereNotNull('payment_at');
                    } else {
                        $q->whereNull('payment_at');
                    }
                }),
            ])
            ->defaultSort('-id');

        return JsonResource::collection($builder->paginate($this->getPerPage()));
    }


    public function batchPayment(Request $request, MatchCenter $matchCenter): JsonResource
    {
        $validated = $request->validate([
            'ids' => ['array', 'required'],
            'ids.*' => ['int', 'required', (new Exists(User::class, 'id'))->where('match_center_id', $matchCenter->id)],
        ]);
        $ids = Arr::get($validated, 'ids');
        // 批量已付款
        User::query()
            ->whereIn('id', $ids)
            ->where('match_center_id', $matchCenter->id)
            ->update([
                'payment_at' => now()
            ]);
        return JsonResource::make([]);
    }

    public function publish(Request $request, MatchCenter $matchCenter): JsonResource
    {
        $validated = $request->validate([
            'ids' => ['array', 'required'],
            'ids.*' => ['int', 'required', (new Exists(User::class, 'id'))->where('match_center_id', $matchCenter->id)],
        ]);
        $ids = Arr::get($validated, 'ids');
        // 批量已付款
        User::query()
            ->whereIn('id', $ids)
            ->where('match_center_id', $matchCenter->id)
            ->chunkMap(function (User $user) {
                $user->update(['status' => UserStatus::Published]);
            });
        return JsonResource::make([]);
    }

    public function import(Request $request, MatchCenter $matchCenter): JsonResource
    {
        $file = $request->file('file');
        if (empty($file)) {
            throw new DataException("请上传文件");
        }
        try {
            $import = new UserImport($matchCenter);
            Excel::import($import, $file);
            if ($error = $import->getErrors()) {
                return JsonResource::make([])->additional(['errors' => $error]);
            }
        } catch (\Throwable $e) {
            throw new DataException("导入失败");
        }
        return JsonResource::make([]);
    }

    public function batchDestroy(Request $request, MatchCenter $matchCenter): JsonResource
    {
        $validated = $request->validate([
            'ids' => ['array', 'required'],
            'ids.*' => ['int', 'required', (new Exists(User::class, 'id'))->where('match_center_id', $matchCenter->id)],
        ]);
        $ids = Arr::get($validated, 'ids');
        // 批量已付款
        User::query()
            ->whereIn('id', $ids)
            ->where('match_center_id', $matchCenter->id)
            ->chunkMap(function (User $user) {
                $user->delete();
            });
        return JsonResource::make([]);
    }

    /**
     * 刪除用戶
     * @param Request $request
     * @param User $user
     * @return JsonResource
     */
    public function destroy(Request $request, MatchCenter $matchCenter, User $user): JsonResource
    {
        $user->delete();
        return JsonResource::make([]);
    }

    public function channel(Request $request, MatchCenter $matchCenter, User $user): JsonResource
    {
        $user->update([
            'status' => UserStatus::Canceled
        ]);
        return JsonResource::make($user);
    }

    public function logOut(Request $request, MatchCenter $matchCenter): JsonResource
    {
        $validated = $request->validate([
            'team_name' => 'required'
        ]);
        $teamName = Arr::get($validated, 'team_name');
        User::query()
            ->where('team_name', $teamName)
            ->where('match_center_id', $matchCenter->id)
            ->chunkMap(function (User $user) {
                $user->update(['status' => UserStatus::LogOut]);
            });
        return JsonResource::make([]);
    }

    /**
     * 修改用户
     * @param Request $request
     * @param User $user
     * @return JsonResource
     */
    public function update(Request $request, MatchCenter $matchCenter, User $user): JsonResource
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'image_url' => 'required|string|max:255',
            'team_name' => 'required|string|max:255',
            'card_type' => ['required', 'in:1,2'],
            'id_card' => ['required', 'string'],
            'is_seeded_player' => ['required', 'bool'],
            'sex' => ['required', new Enum(UserSex::class)],
            'type' => ['required', new Enum(UserType::class)],

            'match_center_project_id' => ['required_if:type,1', new Exists(MatchCenterProject::class, 'id')],
            'match_center_group_id' => ['required_if:type,1', new Exists(MatchCenterGroup::class, 'id')],
            'match_center_level_id' => ['required_if:type,1', new Exists(MatchCenterGroupLevel::class, 'id')],
            'registration_fee' => ['required_if:type,1', 'numeric'],
        ]);
        if ($validated['card_type'] == 1 && !CommonUtil::validateIDCard($validated['id_card'])) {
            throw ValidationException::withMessages([
                'id_card' => ['身份证格式错误!'],
            ]);
        }
        $user->update($validated);
        return JsonResource::make($user);
    }

    /**
     * 新增报名
     * @param Request $request
     * @param User $user
     * @return JsonResource
     */
    public function store(Request $request, MatchCenter $matchCenter, User $user): JsonResource
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'image_url' => 'required|string|max:255',
            'team_name' => 'required|string|max:255',
            'card_type' => ['required', 'in:1,2'],
            'id_card' => ['required', 'string'],
            'sex' => ['required', new Enum(UserSex::class)],
            'type' => ['required', new Enum(UserType::class)],

            'match_center_project_id' => ['required_if:type,1', new Exists(MatchCenterProject::class, 'id')],
            'match_center_group_id' => ['required_if:type,1', new Exists(MatchCenterGroup::class, 'id')],
            'match_center_level_id' => ['required_if:type,1', new Exists(MatchCenterGroupLevel::class, 'id')],
            'registration_fee' => ['required_if:type,1', 'numeric'],
        ]);
        if ($validated['card_type'] == 1 && !CommonUtil::validateIDCard($validated['id_card'])) {
            throw ValidationException::withMessages([
                'id_card' => ['身份证格式错误!'],
            ]);
        }
        $validated['match_center_id'] = $matchCenter->id;
        $user->fill($validated)->save();
        return JsonResource::make($user);
    }
}
