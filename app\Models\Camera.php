<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Camera extends Model
{
    use HasFactory;

    /**
     * 表名
     */
    protected $table = 'cameras';

    /**
     * 可批量赋值的属性
     */
    protected $fillable = [
        'venue_number',
        'camera_mac',
        'last_report_time',
        'remarks',
    ];

    /**
     * 属性类型转换
     */
    protected $casts = [
        'last_report_time' => 'datetime',
    ];

    /**
     * 日期序列化格式
     */
    protected $dateFormat = 'Y-m-d H:i:s';

    /**
     * 为JSON序列化时格式化日期
     */
    protected function serializeDate(\DateTimeInterface $date): string
    {
        return $date->format('Y-m-d H:i:s');
    }
}
